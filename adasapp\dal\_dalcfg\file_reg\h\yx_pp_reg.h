/*
********************************************************************************
*   FILE       :  PubPara_Reg.H
*   DESCRIPTION:  For registering public parameters
********************************************************************************
*/
#ifndef H_PUBPARA_REG
#define H_PUBPARA_REG
#if 0
#include "yx_pp_misc.h"
/*
********************************************************************************
*                        Define Public Structures
********************************************************************************
*/
typedef enum {
    PARA_TYPE_PRIME,
    PARA_TYPE_SUB
} PUBPARA_TYPE_E;

typedef enum {
    NO_NEED_SIMBAK,    
    NEED_SIMBAK
} PUBPARA_SIMBAK_E;

typedef struct {
    INT16U len;
    INT8U  para_type;
    INT8U  bak_attr;
    INT8U *def_ptr;        
} PUBPARA_REG_T;

/*
********************************************************************************
*                        Define PubParaIDs
********************************************************************************
*/
#ifdef PUBPARA_DEF
#undef PUBPARA_DEF
#endif

#define PUBPARA_DEF(_PARA_ID_, _LENTH_, _PARATYPE_, _BAK_ATTRIB_, _PTR_DEFAULT_)  \
                    _PARA_ID_,

typedef enum {
    #include "yx_pp_reg.def"
    MAX_PUBPARA_ID
} PUBPARA_ID_E;

/*
********************************************************************************
*                        Interface Declarations
********************************************************************************
*/
/*FUNCTION   : YX_PubPara_GetRegInfor()
* DESC       : get pubpara register information appointed by "paraid".
* INPUT PARA : paraid
* OUTPUT PARA: reginfor
* RETURNS    : return true when operate sucessfully, or return false
*/
BOOLEAN YX_PubPara_GetRegInfor(INT8U paraid, PUBPARA_REG_T *reginfor);

/*FUNCTION   : YX_PubPara_GetMaxRegItem()
* DESC       : get maximum registered pubpara number.
* RETURNS    : max registered para number
*/
INT8U YX_PubPara_GetMaxRegItem(void);
#endif
#include "dal_pp_reg.h"
/*
********************************************************************************
*/
#endif


