/****************************************************************
**                                                              *
**  FILE         :  RamImage.C                                  *
****************************************************************/
#define  GLOBAL_RAMIMAGE
#include "yx_includes.h"
#include "yx_tools.h"
#include "yx_debug.h"
#include "ramimage.h"

#pragma arm section zidata = "NVZI"
static INT8U RamImage_mem[SIZE_RAMIMMAGE];
#pragma arm section zidata

BOOLEAN RamImageValid(INT8U ParaID)
{
    INT8U  *iptr;
    INT8U   chksum;
    INT16U  size;

    if (ParaID >= sizeof(ParaTbl)/sizeof(ParaTbl[0])){
        return FALSE;
    }
    iptr   = (INT8U *)(RamImage_mem + ParaTbl[ParaID].offset);
    size   = ParaTbl[ParaID].size / 2;
    chksum = *(iptr + size - 1);
    if (chksum != YX_GetChkSum_N(iptr, size - 1)){
       return FALSE;
    }
    if (YX_CmpString(iptr, iptr + size, size) != STR_EQUAL){
       return FALSE;
    }
    else return TRUE;
}

BOOLEAN ResumeRamImage(INT8U ParaID, INT8U *ptr, INT16U len)
{
    INT8U *iptr;
    INT16U size;
    
    if (ParaID >= sizeof(ParaTbl)/sizeof(ParaTbl[0])){
       return FALSE;
    }
    size = ParaTbl[ParaID].size / 2;
    if (len >= size){

       return FALSE;
    }
    if (!RamImageValid(ParaID)) {
       return FALSE;
    }
    
    iptr = (INT8U *)(RamImage_mem + ParaTbl[ParaID].offset); 
    memcpy(ptr, iptr, len);
    return TRUE;
}

BOOLEAN StoreRamImage(INT8U ParaID, INT8U *ptr, INT16U len)
{
    INT8U *iptr;
    INT16U size;
    
    if (ParaID >= sizeof(ParaTbl)/sizeof(ParaTbl[0])) return FALSE;
    size = ParaTbl[ParaID].size / 2;
    if (len >= size) return FALSE;
    
    iptr = (INT8U *)(RamImage_mem + ParaTbl[ParaID].offset);
    memcpy(iptr, ptr, len);
    *(iptr + size - 1) = YX_GetChkSum_N(iptr, size - 1);
    memcpy(iptr + size, iptr, size);
    return TRUE;
}

