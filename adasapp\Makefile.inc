#include path


INCLUDES += -I ../libpki/pki_sdk/
INCLUDES += -I ../../depend/include/
INCLUDES += -I ../../depend/include/cjson/
INCLUDES += -I ./includes/
INCLUDES += -I ./utility/h/
INCLUDES += -I ./kernel/h/
INCLUDES += -I ./port/h/
INCLUDES += -I ./dal/uart/h/
INCLUDES += -I ./dal/gps/h/
INCLUDES += -I ./dal/nvram/h/
INCLUDES += -I ./dal/comm/h/
INCLUDES += -I ./dal/filedrv/h/
INCLUDES += -I ./dal/_dalcfg/file_reg/h/
INCLUDES += -I ./dal/_dalcfg/nv_reg/h/
INCLUDES += -I ./dal/odo/h/
INCLUDES += -I ./gsm/gsm_drv/h/
INCLUDES += -I ./gsm/net_drv/h/
INCLUDES += -I ./app/protocol/h/
INCLUDES += -I ./app/alm/h/
INCLUDES += -I ./app/general/h/
INCLUDES += -I ./app/150g3/h/
INCLUDES += -I ./app/m3/h/
INCLUDES += -I ./app/ner/h/
INCLUDES += -I ./app/wdown/h/
INCLUDES += -I ./app/security/h/
INCLUDES += -I ./app/vse/h/
INCLUDES += -I ./app/pki/h/
INCLUDES += -I ./port_ex/h/

SOURCES = main_entry.c
	    
SOURCES += port_ex/c/ql_uart.c       \
	   port_ex/c/ql_spi.c        \
	   port_ex/c/ql_i2c.c        \

SOURCES += utility/c/YX_List.c	\
	utility/c/YX_Stream.c	\
	utility/c/YX_Tools.c	\
	utility/c/GB_UNTB.c	\
	utility/c/UN_GBTB.c	\
	utility/c/YX_RoundBuf.c \
	utility/c/md5sum.c	\
	utility/c/base64.c	\
				
SOURCES += port/c/YX_PORT.c\
	port/c/YX_PORT_Debug.c\
	port/c/YX_PORT_GPIO.c\
	port/c/YX_PORT_Uart.c\
        port/c/YX_PORT_EVENT.c \
	port/c/YX_PORT_MsgHdl.c\
	port/c/YX_PORT_Gprs.c	\
	port/c/YX_PORT_Sock.c	\
	port/c/YX_PORT_FileSys.c	\
	port/c/YX_PORT_Wifi.c \
	port/c/YX_PORT_PowerSave.c\
	port/c/YX_PORT_Ssl.c\
	port/c/YX_PORT_Dmt.c\
	port/c/YX_PORT_Vse.c\
        port/c/YX_PORT_Gnss.c\

SOURCES += kernel/c/YX_Diagnose.c	\
	kernel/c/YX_Debug.c	\
	kernel/c/YX_Memory.c	\
	kernel/c/YX_Msgman.c	\
	kernel/c/YX_Timer.c

SOURCES += dal/uart/c/YX_UART.c


SOURCES += dal/gps/c/YX_GpsDrv.c\
        dal/gps/c/YX_GpsFilter.c\
        dal/gps/c/YX_GpsRecer.c\
        dal/gps/c/YX_GpsCtl.c\
        dal/gps/c/YX_GpsTool.c\
        dal/gps/c/YX_Systime.c\
        dal/gps/c/YX_ReviseTime.c

SOURCES += dal/filedrv/c/dal_db_drv.c\
        dal/filedrv/c/dal_fs_errman.c\
        dal/filedrv/c/dal_fs_man.c\
        dal/filedrv/c/dal_pp_drv.c\
        dal/filedrv/c/YX_PORT_BackupMem.c\
        dal/filedrv/c/YX_PP_BACKUP.c\
        dal/filedrv/c/YX_RecordSave.c\
        
SOURCES += dal/_dalcfg/file_reg/c/YX_PP_Reg.c\
        dal/_dalcfg/file_reg/c/YX_RecSaveRegister.c
        
SOURCES += dal/_dalcfg/nv_reg/c/YX_nvram_reg.c
                
SOURCES += dal/nvram/c/YX_nvram_item.c\
        dal/nvram/c/YX_nvram_mem.c\
        dal/nvram/c/YX_nvram_moni.c\
        dal/nvram/c/YX_nvram_rec.c

SOURCES += dal/comm/c/YX_VirCommTsk.c\
        dal/comm/c/YX_InputDrv.c\
        dal/comm/c/YX_MainInputDrv.c\
        dal/comm/c/YX_OutputDrv.c\
        dal/comm/c/YX_PortDrv.c\
        dal/comm/c/YX_Sensor.c\
        dal/comm/c/yx_para_store.c\
        dal/comm/c/sha204_drv.c\
        dal/comm/c/sha256.c\
        dal/comm/c/sha204_helper.c\
        dal/comm/c/yx_wifi_drv.c\

SOURCES += dal/odo/c/YX_OdoMeter.c\
        dal/odo/c/YX_GpsOdoMeter.c\
        dal/odo/c/YX_PulseOdoMeter.c\
        dal/odo/c/YX_OdoMeterTrigger.c\
        dal/odo/c/YX_VtCalculate.c\

SOURCES += gsm/gsm_drv/c/YX_VirGsmTsk.c\
        gsm/gsm_drv/c/AT_CMD.c\
        gsm/gsm_drv/c/AT_CORE.c\
        gsm/gsm_drv/c/AT_RECV.c\
        gsm/gsm_drv/c/AT_SET.c\
        gsm/gsm_drv/c/AT_SM.c\
        gsm/gsm_drv/c/AT_TRANS.c\
        gsm/gsm_drv/c/AT_VOICE.c\
        gsm/gsm_drv/c/PhoneDrv.c\
        gsm/gsm_drv/c/PDUMode.c\
        gsm/gsm_drv/c/SM_List.c\
        gsm/gsm_drv/c/SM_RECV.c\
        gsm/gsm_drv/c/GsmIo.c\
        gsm/gsm_drv/c/ril_gb_to_ucs2.c\
        gsm/gsm_drv/c/ril_ucs2_to_gb.c\
        gsm/gsm_drv/c/ril_utils.c\
        gsm/gsm_drv/c/ril_pdu_cdma.c\

SOURCES += gsm/net_drv/c/YX_GprsDrv.c\
				gsm/net_drv/c/YX_TCPDrv.c\
				gsm/net_drv/c/YX_UDPDrv.c\

SOURCES += app/protocol/c/GPRSLink.c\
				app/protocol/c/yx_jt_tlink.c\
				app/protocol/c/yx_jt_trecv.c\
				app/protocol/c/yx_jt_tsend.c\
				app/protocol/c/yx_jt_ulink.c\
				app/protocol/c/yx_jt_urecv.c\
				app/protocol/c/yx_jt_usend.c\
				app/protocol/c/yx_jt2_tlink.c\
				app/protocol/c/yx_jt2_trecv.c\
				app/protocol/c/yx_jt2_tsend.c\
				app/protocol/c/yx_jt_linkman.c\
				app/protocol/c/YX_RX_Frame.c\
				app/protocol/c/YX_SMBackup.c\
				app/protocol/c/yx_winrecv.c\
				app/protocol/c/yx_wintrans.c\
				app/protocol/c/yx_hearttable.c\
				app/protocol/c/SMSFrame.c\
				app/protocol/c/SMSConfig.c\
				app/protocol/c/YX_SYSFrame.c\

SOURCES += app/ner/c/yx_ne1_tlink.c \
				app/ner/c/yx_ne1_trecv.c \
				app/ner/c/yx_ne1_tsend.c \
				app/ner/c/yx_ne2_tlink.c \
				app/ner/c/yx_ne2_trecv.c \
				app/ner/c/yx_ne2_tsend.c \
				app/ner/c/yx_ne_sysframe.c \
				app/ner/c/yx_ne_rxframe.c \
				app/ner/c/yx_ne_ctrman.c \
				app/ner/c/yx_ne_hdlcan.c \
				app/ner/c/yx_ne_linkman.c \
				app/ner/c/yx_ne_posrept.c \
				app/ner/c/yx_ne_resend.c \
				app/ner/c/yx_timerec_reg.c \
				app/ner/c/yx_timerec_drv.c \
				app/ner/c/yx_ne_recdata.c\
                                app/ner/c/yx_ne_sign.c\

SOURCES += app/general/c/YX_VirOptTsk.c\
				app/general/c/YX_AppTool.c\
				app/general/c/YX_ParaManage.c\
				app/general/c/YX_GeneralMan.c\
				app/general/c/YX_regincode.c\
				app/general/c/yx_resend.c\
				app/general/c/yx_retransmit.c\
				app/general/c/YX_ResetCnt.c\
				app/general/c/yx_sm_cmd.c\
				app/general/c/yx_cmdline.c\
				app/general/c/YX_AutoDial.c\
				app/general/c/HardwareCheck.c\
				app/general/c/elecquantity.c\
				app/general/c/YX_VectorTrigger.c\
				app/general/c/yx_super_lowpower.c\
				app/general/c/yx_agps_control.c\
				app/general/c/yx_agps_tlink.c\
				app/general/c/yx_candata.c\
				app/general/c/yx_carlock_control.c\
				app/general/c/yx_liblock.c\
				app/general/c/yx_authentication.c\
				app/general/c/yx_cancollect.c\
				app/general/c/yx_factory_detect.c\
				app/general/c/yx_log_man.c\
				app/general/c/yx_network_flow_man.c\

SOURCES += app/150g3/c/yx_150g3_com.c\
				app/150g3/c/yx_150g3_drv.c\
				app/150g3/c/yx_150g3_recv.c\
				app/150g3/c/yx_150g3_send.c\
				app/150g3/c/yx_120r_can.c\

SOURCES += app/m3/c/app_m3_drv.c \
				app/m3/c/app_m3_ner.c \
				app/m3/c/app_m3_ner0x01.c \
				app/m3/c/app_m3_ner0x02.c \
				app/m3/c/app_m3_ner0x03.c \
				app/m3/c/app_m3_ner0x04.c \
				app/m3/c/app_m3_ner0x05.c \
				app/m3/c/app_m3_ner0x06.c \
				app/m3/c/app_m3_ner0x07.c \
				app/m3/c/app_m3_ner0x08.c \
				app/m3/c/app_m3_ner0x09.c \
				app/m3/c/app_m3_ner0x30.c \
				app/m3/c/app_m3_ner0x80.c \
				app/m3/c/md5.c\

SOURCES += app/security/c/sec_trans.c\
				app/security/c/sec_selftest.c\

SOURCES += app/wdown/c/yx_wd_man.c\
				app/wdown/c/yx_wd_frame.c\
				app/wdown/c/yx_wd_paraset.c\
				app/wdown/c/yx_wd_tlink.c\
				app/wdown/c/yx_wd_trecv.c\
				app/wdown/c/yx_wd_ulink.c\
				app/wdown/c/yx_wd_urecv.c\
				app/wdown/c/yx_http_ota.c\
				app/wdown/c/yx_https_ota.c\

SOURCES += app/alm/c/YX_Moniser.c\
				app/alm/c/YX_Monitor.c\
				app/alm/c/YX_PosRept.c\
				app/alm/c/YX_alarmer.c\
				app/alm/c/YX_alarm_protocol.c\
				app/alm/c/yx_smstextalarm.c\
				app/alm/c/yx_alarm_reg.c\
				app/alm/c/YX_alarm_rob.c\
				app/alm/c/YX_alarm_displace.c\
				app/alm/c/YX_alarm_overspeed.c\
				app/alm/c/YX_alarm_power.c\
				app/alm/c/YX_alarm_vind.c\
				app/alm/c/YX_defencer.c\
				app/alm/c/YX_alarm_guard.c\
				app/alm/c/YX_alarm_gps.c\
				app/alm/c/YX_alarm_tired.c\

SOURCES += app/pki/c/yx_pki_man.c\

SUBDIRS= customerconfig\
					utility/c\
					port/c\
					kernel/c\
					dal/uart/c\
					dal/gps/c\
					dal/nvram/c\
					dal/fsys/c\
					dal/_dalcfg/file_reg/c\
					dal/_dalcfg/nv_reg/c\
					dal/comm/c\
					dal/odo/c\
					gsm/gsm_drv/c\
					gsm/net_drv/c\
					app/protocol/c\
					app/alm/c\
					app/general/c\
					app/150g3/c\
					app/m3/c\
					app/ner/c\
					app/wdown/c\
					app/security/c\
					app/vse/c\

