# Copyright (C) 2020 QUECTEL Technologies Limited and/or its affiliates("QUECTEL").
# All rights reserved.
#

configure_file (
    "${rz_app_dir}/ql_app_feature_config.h.in"
    "${out_inc_dir}/ql_app_feature_config.h"
)

if (QL_APP_PACK_FILE)
if (QL_CCSDK_BUILD)
set(CONFIG_PACKAGE_FILE_APPIMG_JSON_PATH ${QL_APP_PACK_FILE_JSON_PATH})
else()
set(CONFIG_PACKAGE_FILE_APPIMG_JSON_PATH $ENV{prepack_json_path})
endif()
endif()

if(NOT QL_PROJECT_MIXER)
	set(target ${QL_APP_BUILD_VER})
	if(CONFIG_APPIMG_LOAD_FLASH)
		add_appimg_flash_ql_example(${target} ql_init.c)
		target_link_libraries(${target} PRIVATE ql_app_adastbox)
		target_link_libraries(${target} PRIVATE ${libc_file_name} ${libm_file_name} ${libgcc_file_name})
		target_link_libraries(${target} PRIVATE ${SOURCE_TOP_DIR}/components/customer/_install/lib/libdmtcrypt.a)
	endif()
	if(CONFIG_APPIMG_LOAD_FILE)
		add_appimg_file_ql_example(${target} ql_init.c)
		target_link_libraries(${target} PRIVATE ql_app_adastbox)
		target_link_libraries(${target} PRIVATE ${libc_file_name} ${libm_file_name} ${libgcc_file_name})
		target_link_libraries(${target} PRIVATE ${SOURCE_TOP_DIR}/components/customer/_install/lib/libdmtcrypt.a)
	endif()
endif()

add_compile_options(
    -Wno-missing-braces
)


add_subdirectory_if_exist(adasapp)

set(target ql_app_adastbox)

add_library(${target} STATIC)
set_target_properties(${target} PROPERTIES ARCHIVE_OUTPUT_DIRECTORY ${out_app_lib_dir})
target_compile_definitions(${target} PRIVATE OSI_LOG_TAG=LOG_TAG_QUEC)

target_include_directories(${target} PUBLIC adasapp)
target_include_directories(${target} PUBLIC adasapp/includes/)
target_include_directories(${target} PUBLIC adasapp/utility/h/)
target_include_directories(${target} PUBLIC adasapp/kernel/h/)
target_include_directories(${target} PUBLIC adasapp/port/h/)
target_include_directories(${target} PUBLIC adasapp/dal/uart/h/)
target_include_directories(${target} PUBLIC adasapp/dal/gps/h/)
target_include_directories(${target} PUBLIC adasapp/dal/nvram/h/)
target_include_directories(${target} PUBLIC adasapp/dal/comm/h/)
target_include_directories(${target} PUBLIC adasapp/dal/filedrv/h/)
target_include_directories(${target} PUBLIC adasapp/dal/_dalcfg/file_reg/h/)
target_include_directories(${target} PUBLIC adasapp/dal/_dalcfg/nv_reg/h/)
target_include_directories(${target} PUBLIC adasapp/dal/odo/h/)
target_include_directories(${target} PUBLIC adasapp/gsm/gsm_drv/h/)
target_include_directories(${target} PUBLIC adasapp/gsm/net_drv/h/)
target_include_directories(${target} PUBLIC adasapp/app/protocol/h/)
target_include_directories(${target} PUBLIC adasapp/app/general/h/)
target_include_directories(${target} PUBLIC adasapp/app/150g3/h/)
target_include_directories(${target} PUBLIC adasapp/app/m3/h/)
target_include_directories(${target} PUBLIC adasapp/app/ner/h/)
target_include_directories(${target} PUBLIC adasapp/app/wdown/h/)
#target_link_libraries(${target} PRIVATE  ql_api_common)

target_sources(${target} PRIVATE
	adasapp/ql_pin_config.c
	adasapp/main_entry.c
	adasapp/utility/c/YX_List.c
	adasapp/utility/c/YX_Stream.c
	adasapp/utility/c/YX_Tools.c
	adasapp/utility/c/GB_UNTB.c
	adasapp/utility/c/UN_GBTB.c
	adasapp/utility/c/YX_RoundBuf.c

	adasapp/port/c/YX_PORT.c
	adasapp/port/c/YX_PORT_MsgHdl.c
	adasapp/port/c/YX_PORT_Uart.c
	adasapp/port/c/YX_PORT_FileSys.c
	adasapp/port/c/YX_PORT_Gprs.c
	adasapp/port/c/YX_PORT_Sock.c
	adasapp/port/c/YX_PORT_GPIO.c
	adasapp/port/c/YX_PORT_Debug.c
	adasapp/port/c/yx_port_sms.c

	adasapp/kernel/c/YX_Diagnose.c
	adasapp/kernel/c/YX_Debug.c
	adasapp/kernel/c/YX_Memory.c
	adasapp/kernel/c/YX_Msgman.c
	adasapp/kernel/c/YX_Timer.c

	adasapp/dal/uart/c/YX_UART.c

	adasapp/gsm/gsm_drv/c/YX_VirGsmTsk.c
	adasapp/gsm/gsm_drv/c/AT_CMD.c
	adasapp/gsm/gsm_drv/c/AT_CORE.c
	adasapp/gsm/gsm_drv/c/AT_RECV.c
	adasapp/gsm/gsm_drv/c/AT_SET.c
	adasapp/gsm/gsm_drv/c/AT_SM.c
	adasapp/gsm/gsm_drv/c/AT_TRANS.c
	adasapp/gsm/gsm_drv/c/AT_VOICE.c
	adasapp/gsm/gsm_drv/c/PhoneDrv.c
	adasapp/gsm/gsm_drv/c/PDUMode.c
	adasapp/gsm/gsm_drv/c/SM_List.c
	adasapp/gsm/gsm_drv/c/SM_RECV.c
	adasapp/gsm/gsm_drv/c/GsmIo.c

	adasapp/gsm/net_drv/c/YX_GprsDrv.c
	adasapp/gsm/net_drv/c/YX_TCPDrv.c
	adasapp/gsm/net_drv/c/YX_UDPDrv.c

	adasapp/dal/comm/c/YX_VirCommTsk.c
	adasapp/dal/comm/c/YX_InputDrv.c
	adasapp/dal/comm/c/YX_MainInputDrv.c
	adasapp/dal/comm/c/YX_OutputDrv.c
	adasapp/dal/comm/c/YX_PortDrv.c
	adasapp/dal/comm/c/YX_Sensor.c
	adasapp/dal/comm/c/yx_para_store.c
	adasapp/dal/comm/c/sha204_drv.c
	adasapp/dal/comm/c/sha256.c
	adasapp/dal/comm/c/sha204_helper.c

	adasapp/dal/filedrv/c/dal_fs_errman.c
	adasapp/dal/filedrv/c/dal_fs_man.c
	adasapp/dal/filedrv/c/dal_pp_drv.c
	adasapp/dal/filedrv/c/YX_PP_Backup.c
	adasapp/dal/filedrv/c/YX_RecordSave.c

	adasapp/dal/_dalcfg/file_reg/c/dal_pp_reg.c
	adasapp/dal/_dalcfg/file_reg/c/dal_fs_cfg.c
	adasapp/dal/_dalcfg/file_reg/c/YX_RecSaveRegister.c

	adasapp/dal/_dalcfg/nv_reg/c/YX_nvram_reg.c

	adasapp/dal/nvram/c/YX_nvram_item.c
	adasapp/dal/nvram/c/YX_nvram_mem.c
	adasapp/dal/nvram/c/YX_nvram_moni.c
	adasapp/dal/nvram/c/YX_nvram_rec.c

	adasapp/dal/gps/c/YX_GpsDrv.c
	adasapp/dal/gps/c/YX_GpsFilter.c
	adasapp/dal/gps/c/YX_GpsRecer.c
	adasapp/dal/gps/c/YX_GpsCtl.c
	adasapp/dal/gps/c/YX_GpsTool.c
	adasapp/dal/gps/c/YX_Systime.c
	adasapp/dal/gps/c/YX_ReviseTime.c

	adasapp/dal/odo/c/YX_OdoMeter.c
	adasapp/dal/odo/c/YX_GpsOdoMeter.c
	adasapp/dal/odo/c/YX_PulseOdoMeter.c
	adasapp/dal/odo/c/YX_OdoMeterTrigger.c
	adasapp/dal/odo/c/YX_VtCalculate.c

	adasapp/app/protocol/c/GPRSLink.c
	adasapp/app/protocol/c/yx_jt_tlink.c
	adasapp/app/protocol/c/yx_jt_ulink.c
	adasapp/app/protocol/c/yx_hearttable.c
	adasapp/app/protocol/c/SMSFrame.c

	adasapp/app/ner/c/app_timerec_drv.c
	adasapp/app/ner/c/app_timerec_reg.c

	adasapp/app/wdown/c/yx_wd_man.c
	adasapp/app/wdown/c/yx_wd_frame.c
	adasapp/app/wdown/c/yx_wd_paraset.c
	adasapp/app/wdown/c/yx_wd_tlink.c
	adasapp/app/wdown/c/yx_wd_trecv.c
	adasapp/app/wdown/c/yx_wd_ulink.c
	adasapp/app/wdown/c/yx_wd_urecv.c

	adasapp/app/150g3/c/yx_150g3_com.c
	adasapp/app/150g3/c/yx_150g3_drv.c
	adasapp/app/150g3/c/yx_150g3_recv.c
	adasapp/app/150g3/c/yx_150g3_send.c

	adasapp/app/general/c/YX_VirOptTsk.c
	adasapp/app/general/c/YX_ResetCnt.c
	adasapp/app/general/c/yx_sm_cmd.c
	adasapp/app/general/c/yx_cmdline.c
	adasapp/app/general/c/HardwareCheck.c
	adasapp/app/general/c/yx_authentication.c

	adasapp/app/m3/c/app_m3_drv.c
	adasapp/app/m3/c/app_m3_ner.c
	adasapp/app/m3/c/app_m3_ner0x01.c
	adasapp/app/m3/c/app_m3_ner0x02.c
	adasapp/app/m3/c/app_m3_ner0x03.c
	adasapp/app/m3/c/app_m3_ner0x04.c
	adasapp/app/m3/c/app_m3_ner0x05.c
	adasapp/app/m3/c/app_m3_ner0x06.c
	adasapp/app/m3/c/app_m3_ner0x07.c
	adasapp/app/m3/c/app_m3_ner0x08.c
	adasapp/app/m3/c/app_m3_ner0x09.c
	adasapp/app/m3/c/app_m3_ner0x30.c
	adasapp/app/m3/c/app_m3_ner0x80.c
	adasapp/app/m3/c/app_m3_ner0xb1.c
	adasapp/app/m3/c/app_m3_ner0xb2.c
	adasapp/app/m3/c/md5.c

	adasapp/app/ner/c/app_timerec_drv.c
	adasapp/app/ner/c/app_timerec_reg.c
	adasapp/app/ner/c/app_ner_recv.c
	adasapp/app/ner/c/app_ner_rxframe.c
	adasapp/app/ner/c/app_ner_send.c
	adasapp/app/ner/c/app_ner_sysframe.c
	adasapp/app/ner/c/app_ner_linkman.c
	adasapp/app/ner/c/app_ner2_tlink.c
	adasapp/app/ner/c/yx_ner_hdlcan.c
	adasapp/app/ner/c/app_ner_generalman.c
	adasapp/app/ner/c/app_ner_rept.c
	adasapp/app/ner/c/app_ner_resend.c
	adasapp/app/ner/c/app_ner_sendrecdata.c
)

#target_sources_if(QL_APP_FEATURE_KEYPAD THEN ${target} PRIVATE app/app_other/c/keypad_demo.c)
#target_sources_if(QL_APP_FEATURE_RS485	THEN ${target} PRIVATE app/app_other/c/rs485_demo.c)

relative_glob(srcs include/*.h src/*.c inc/*.h)
beautify_c_code(${target} ${srcs})


