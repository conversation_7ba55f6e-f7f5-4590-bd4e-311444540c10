# Copyright (C) 2020 QUECTEL Technologies Limited and/or its affiliates("QUECTEL").
# All rights reserved.
#

message("\n")

if(CONFIG_QL_OPEN_EXPORT_PKG)

if(CONFIG_QUEC_PROJECT_FEATURE_SECURE_BOOT)
option(QL_APP_FEATURE_SECURE_BOOT  "Enable SECURE BOOT" OFF)
else()
message(STATUS "FEATURE SECURE BOOT is disabled at core!")
option(QL_APP_FEATURE_SECURE_BOOT  "Enable SECURE BOOT" OFF)
endif()
if(CONFIG_QUEC_PROJECT_FEATURE_GNSS)
option(QL_APP_FEATURE_GNSS  "Enable GNSS" ON)
else()
message(STATUS "FEATURE GNSS is disabled at core!")
option(QL_APP_FEATURE_GNSS  "Enable GNSS" OFF)
endif()
message(STATUS "QL_APP_FEATURE_GNSS ${QL_APP_FEATURE_GNSS}")


################################################################################################################
# Quectel open sdk feature config
################################################################################################################
if(CONFIG_QUEC_PROJECT_FEATURE_FTP)
option(QL_APP_FEATURE_FTP  "Enable FTP" ON)
else()
message(STATUS "FEATURE FTP is disabled at core!")
option(QL_APP_FEATURE_FTP  "Enable FTP" OFF)
endif()
message(STATUS "QL_APP_FEATURE_FTP ${QL_APP_FEATURE_FTP}")

if(CONFIG_QUEC_PROJECT_FEATURE_HTTP)
option(QL_APP_FEATURE_HTTP  "Enable HTTP" ON)
else()
message(STATUS "FEATURE HTTP is disabled at core!")
option(QL_APP_FEATURE_HTTP  "Enable HTTP" OFF)
endif()
message(STATUS "QL_APP_FEATURE_HTTP ${QL_APP_FEATURE_HTTP}")

if (CONFIG_QUEC_PROJECT_FEATURE_MMS)
option(QL_APP_FEATURE_MMS  "Enable MMS" ON)
else()
message(STATUS "FEATURE MMS is disabled at core!")
option(QL_APP_FEATURE_MMS  "Enable MMS" OFF)
endif()
message(STATUS "QL_APP_FEATURE_MMS ${QL_APP_FEATURE_MMS}")

if(CONFIG_QUEC_PROJECT_FEATURE_MQTT)
option(QL_APP_FEATURE_MQTT  "Enable MQTT" ON)
else()
message(STATUS "FEATURE MQTT is disabled at core!")
option(QL_APP_FEATURE_MQTT  "Enable MQTT" OFF)
endif()
message(STATUS "QL_APP_FEATURE_MQTT ${QL_APP_FEATURE_MQTT}")

if(CONFIG_QUEC_PROJECT_FEATURE_SSL)
option(QL_APP_FEATURE_SSL  "Enable SSL" ON)
else()
message(STATUS "FEATURE SSL is disabled at core!")
option(QL_APP_FEATURE_SSL  "Enable SSL" OFF)
endif()
message(STATUS "QL_APP_FEATURE_SSL ${QL_APP_FEATURE_SSL}")

if(CONFIG_QUEC_PROJECT_FEATURE_PING)
option(QL_APP_FEATURE_PING  "Enable PING" ON)
else()
message(STATUS "FEATURE PING is disabled at core!")
option(QL_APP_FEATURE_PING  "Enable PING" OFF)
endif()
message(STATUS "QL_APP_FEATURE_PING ${QL_APP_FEATURE_PING}")

if(CONFIG_QUEC_PROJECT_FEATURE_NTP)
option(QL_APP_FEATURE_NTP  "Enable NTP" ON)
else()
message(STATUS "FEATURE NTP is disabled at core!")
option(QL_APP_FEATURE_NTP  "Enable NTP" OFF)
endif()
message(STATUS "QL_APP_FEATURE_NTP ${QL_APP_FEATURE_NTP}")

if(CONFIG_QUEC_PROJECT_FEATURE_SSL)
option(QL_APP_FEATURE_ALI_LINKSDK  "Enable ALI_LINKSDK" ON)
else()
message(STATUS "FEATURE ALI_LINKSDK is disabled at core!")
option(QL_APP_FEATURE_ALI_LINKSDK  "Enable ALI_LINKSDK" OFF)
endif()
message(STATUS "QL_APP_FEATURE_ALI_LINKSDK ${QL_APP_FEATURE_ALI_LINKSDK}")

if(CONFIG_QUEC_PROJECT_FEATURE_SSL)
option(QL_APP_FEATURE_QCLOUD_IOT  "Enable QCLOUD_IOT" ON)
else()
message(STATUS "FEATURE QCLOUD_IOT is disabled at core!")
option(QL_APP_FEATURE_QCLOUD_IOT  "Enable QCLOUD_IOT" OFF)
endif()
message(STATUS "QL_APP_FEATURE_QCLOUD_IOT ${QL_APP_FEATURE_QCLOUD_IOT}")

if(CONFIG_QUEC_PROJECT_FEATURE_LBS)
option(QL_APP_FEATURE_LBS  "Enable LBS" ON)
else()
message(STATUS "FEATURE LBS is disabled at core!")
option(QL_APP_FEATURE_LBS  "Enable LBS" OFF)
endif()
message(STATUS "QL_APP_FEATURE_LBS ${QL_APP_FEATURE_LBS}")

if(CONFIG_QUEC_PROJECT_FEATURE_QTHSDK)
option(QL_APP_FEATURE_QTHSDK  "Enable QTHSDK" ON)
else()
message(STATUS "FEATURE QTHSDK is disabled at core!")
option(QL_APP_FEATURE_QTHSDK  "Disable QTHSDK" OFF)
endif()
message(STATUS "QL_APP_FEATURE_QTHSDK ${QL_APP_FEATURE_QTHSDK}")

if(CONFIG_QUEC_PROJECT_FEATURE_SOCKET)
option(QL_APP_FEATURE_SOCKET  "Enable Socket" ON)
else()
message(STATUS "FEATURE SOCKET is disabled at core!")
option(QL_APP_FEATURE_SOCKET  "Enable Socket" OFF)
endif()
message(STATUS "QL_APP_FEATURE_SOCKET ${QL_APP_FEATURE_SOCKET}")

if(CONFIG_QUEC_PROJECT_FEATURE_CTSREG)
option(QL_APP_FEATURE_CTSREG  "Enable CTSREG" ON)
else()
message(STATUS "FEATURE CTSREG is disabled at core!")
option(QL_APP_FEATURE_CTSREG  "Enable CTSREG" OFF)
endif()
message(STATUS "QL_APP_FEATURE_CTSREG ${QL_APP_FEATURE_CTSREG}")

if(CONFIG_QUEC_PROJECT_FEATURE_FILE)
option(QL_APP_FEATURE_FILE  "Enable FILE" ON)
else()
message(STATUS "FEATURE FILE is disabled at core!")
option(QL_APP_FEATURE_FILE  "Enable FILE" OFF)
endif()
message(STATUS "QL_APP_FEATURE_FILE ${QL_APP_FEATURE_FILE}")

if((CONFIG_QUEC_PROJECT_FEATURE_FILE_ZIP) AND (CONFIG_QUEC_PROJECT_FEATURE_FILE))
option(QL_APP_FEATURE_FILE_ZIP  "Enable FILE_ZIP" ON)
else()
message(STATUS "FEATURE FILE_ZIP is disabled at core!")
option(QL_APP_FEATURE_FILE_ZIP  "Enable FILE_ZIP" OFF)
endif()
message(STATUS "QL_APP_FEATURE_FILE_ZIP ${QL_APP_FEATURE_FILE_ZIP}")

if(CONFIG_QUEC_PROJECT_FEATURE_AUDIO)
option(QL_APP_FEATURE_AUDIO  "Enable AUDIO" ON)
else()
message(STATUS "FEATURE AUDIO is disabled at core!")
option(QL_APP_FEATURE_AUDIO  "Enable AUDIO" OFF)
endif()
message(STATUS "QL_APP_FEATURE_AUDIO ${QL_APP_FEATURE_AUDIO}")

if((QL_APP_FEATURE_AUDIO) AND (CONFIG_QUEC_PROJECT_FEATURE_HEADSET_DET))
option(QL_APP_FEATURE_HEADSET_DET  "Enable HEADSET_DET" ON)
else()
message(STATUS "FEATURE HEADSET_DET is disabled at core!")
option(QL_APP_FEATURE_HEADSET_DET  "Enable HEADSET_DET" OFF)
endif()
message(STATUS "QL_APP_FEATURE_HEADSET_DET ${QL_APP_FEATURE_HEADSET_DET}")

if((QL_APP_FEATURE_AUDIO) AND (CONFIG_QUEC_PROJECT_FEATURE_TTS))
option(QL_APP_FEATURE_TTS  "Enable TTS" ON)
else()
message(STATUS "APP FEATURE AUDIO is ${QL_APP_FEATURE_AUDIO}, FEATURE TTS at core is ${CONFIG_QUEC_PROJECT_FEATURE_TTS}")
option(QL_APP_FEATURE_TTS  "Enable TTS" OFF)
endif()
message(STATUS "QL_APP_FEATURE_TTS ${QL_APP_FEATURE_TTS}")

if((QL_APP_FEATURE_AUDIO) AND (CONFIG_QUEC_PROJECT_FEATURE_EXT_CODEC))
option(QL_APP_FEATURE_EXT_CODEC  "Enable EXT_CODEC" ON)
else()
message(STATUS "APP FEATURE AUDIO is ${QL_APP_FEATURE_AUDIO}, FEATURE EXT_CODEC at core is ${CONFIG_QUEC_PROJECT_FEATURE_EXT_CODEC}")
option(QL_APP_FEATURE_EXT_CODEC  "Enable EXT_CODEC" OFF)
endif()
message(STATUS "QL_APP_FEATURE_EXT_CODEC ${QL_APP_FEATURE_EXT_CODEC}")

if(CONFIG_QUEC_PROJECT_FEATURE_WIFISCAN)
option(QL_APP_FEATURE_WIFISCAN  "Enable WIFI-Scan" ON)
else()
message(STATUS "FEATURE WIFISCAN is disabled at core!")
option(QL_APP_FEATURE_WIFISCAN  "Enable WIFI-Scan" OFF)
endif()
message(STATUS "QL_APP_FEATURE_WIFISCAN ${QL_APP_FEATURE_WIFISCAN}")

if(CONFIG_QUEC_PROJECT_FEATURE_BT)
option(QL_APP_FEATURE_BT  "Enable BT" ON)
else()
message(STATUS "FEATURE BT is disabled at core!")
option(QL_APP_FEATURE_BT  "Enable BT" OFF)
endif()
message(STATUS "QL_APP_FEATURE_BT ${QL_APP_FEATURE_BT}")

if(CONFIG_QUEC_PROJECT_FEATURE_BT_HFP)
option(QL_APP_FEATURE_BT_HFP  "Enable BT HFP" ON)
else()
message(STATUS "FEATURE BT HFP is disabled at core!")
option(QL_APP_FEATURE_BT_HFP  "Enable BT HFP" OFF)
endif()
message(STATUS "QL_APP_FEATURE_BT_HFP ${QL_APP_FEATURE_BT_HFP}")

if(CONFIG_QUEC_PROJECT_FEATURE_BT_A2DP_AVRCP)
option(QL_APP_FEATURE_BT_A2DP_AVRCP  "Enable BT A2DP & AVRCP" ON)
else()
message(STATUS "FEATURE BT A2DP & AVRCP is disabled at core!")
option(QL_APP_FEATURE_BT_A2DP_AVRCP  "Enable BT A2DP & AVRCP" OFF)
endif()
message(STATUS "QL_APP_FEATURE_BT_A2DP_AVRCP ${QL_APP_FEATURE_BT_A2DP_AVRCP}")

if(CONFIG_QUEC_PROJECT_FEATURE_BLE_GATT)
option(QL_APP_FEATURE_BLE_GATT  "Enable BLE GATT" ON)
else()
message(STATUS "FEATURE BLE GATT is disabled at core!")
option(QL_APP_FEATURE_BLE_GATT  "Enable BLE GATT" OFF)
endif()
message(STATUS "QL_APP_FEATURE_BLE_GATT ${QL_APP_FEATURE_BLE_GATT}")

if(CONFIG_QUEC_PROJECT_FEATURE_LCD)
option(QL_APP_FEATURE_LCD  "Enable LCD" ON)
option(QL_APP_FEATURE_LVGL  "Enable LVGL" ON)
else()
message(STATUS "FEATURE LCD is disabled at core!")
option(QL_APP_FEATURE_LCD  "Enable LCD" OFF)
option(QL_APP_FEATURE_LVGL  "Enable LVGL" OFF)
endif()
message(STATUS "QL_APP_FEATURE_LCD ${QL_APP_FEATURE_LCD}")
message(STATUS "QL_APP_FEATURE_LVGL ${QL_APP_FEATURE_LVGL}")

if(CONFIG_QUEC_PROJECT_FEATURE_CAMERA)
option(QL_APP_FEATURE_CAMERA  "Enable CAMERA" ON)
else()
message(STATUS "FEATURE CAMERA is disabled at core!")
option(QL_APP_FEATURE_CAMERA  "Enable CAMERA" OFF)
endif()
message(STATUS "QL_APP_FEATURE_CAMERA ${QL_APP_FEATURE_CAMERA}")

if(CONFIG_QUEC_PROJECT_FEATURE_SIM)
option(QL_APP_FEATURE_SIM  "Enable SIM" ON)
else()
message(STATUS "FEATURE SIM is disabled at core!")
option(QL_APP_FEATURE_SIM  "Enable SIM" OFF)
endif()
message(STATUS "QL_APP_FEATURE_SIM ${QL_APP_FEATURE_SIM}")

if(CONFIG_QUEC_PROJECT_FEATURE_QDSIM)
option(QL_APP_FEATURE_DSIM  "Enable DOUBLE SIM" ON)
else()
message(STATUS "FEATURE DOUBLE SIM is disabled at core!")
option(QL_APP_FEATURE_DSIM  "Enable DOUBLE SIM" OFF)
endif()
message(STATUS "QL_APP_FEATURE_DSIM ${QL_APP_FEATURE_DSIM}")

if(CONFIG_QUEC_PROJECT_FEATURE_PBK)
option(QL_APP_FEATURE_PBK  "Enable PBK" ON)
else()
message(STATUS "FEATURE PBK is disabled at core!")
option(QL_APP_FEATURE_PBK  "Enable PBK" OFF)
endif()
message(STATUS "QL_APP_FEATURE_PBK ${QL_APP_FEATURE_PBK}")

if(CONFIG_QUEC_PROJECT_FEATURE_SMS)
option(QL_APP_FEATURE_SMS  "Enable SMS" ON)
else()
message(STATUS "FEATURE SMS is disabled at core!")
option(QL_APP_FEATURE_SMS  "Enable SMS" OFF)
endif()
message(STATUS "QL_APP_FEATURE_SMS ${QL_APP_FEATURE_SMS}")

if(CONFIG_QUEC_PROJECT_FEATURE_VOICE_CALL)
option(QL_APP_FEATURE_VOICE_CALL  "Enable VOICE CALL" ON)
else()
message(STATUS "FEATURE VOICE CALL is disabled at core!")
option(QL_APP_FEATURE_VOICE_CALL  "Enable VOICE CALL" OFF)
endif()
message(STATUS "QL_APP_FEATURE_VOICE_CALL ${QL_APP_FEATURE_VOICE_CALL}")

if(CONFIG_QUEC_PROJECT_FEATURE_VOLTE)
option(QL_APP_FEATURE_VOLTE  "Enable VoLTE CALL" ON)
else()
message(STATUS "FEATURE VoLTE is disabled at core!")
option(QL_APP_FEATURE_VOLTE  "Enable VoLTE CALL" OFF)
endif()
message(STATUS "QL_APP_FEATURE_VOLTE ${QL_APP_FEATURE_VOLTE}")

if((CONFIG_QUEC_PROJECT_FEATURE_SPI) AND (CONFIG_QUEC_PROJECT_FEATURE_GPIO))
option(QL_APP_FEATURE_SPI  "Enable SPI" ON)
else()
message(STATUS "FEATURE SPI is disabled at core!")
option(QL_APP_FEATURE_SPI  "Enable SPI" OFF)
endif()
message(STATUS "QL_APP_FEATURE_SPI ${QL_APP_FEATURE_SPI}")

if((CONFIG_QUEC_PROJECT_FEATURE_SPI_NOR_FLASH) AND (CONFIG_QUEC_PROJECT_FEATURE_GPIO))
option(QL_APP_FEATURE_SPI_NOR_FLASH  "Enable SPI_NOR_FLASH" ON)
else()
message(STATUS "FEATURE SPI_NOR_FLASH is disabled at core!")
option(QL_APP_FEATURE_SPI_NOR_FLASH  "Enable SPI_NOR_FLASH" OFF)
endif()
message(STATUS "QL_APP_FEATURE_SPI_NOR_FLASH ${QL_APP_FEATURE_SPI_NOR_FLASH}")

if((CONFIG_QUEC_PROJECT_FEATURE_SPI4_EXT_NOR_SFFS) AND (CONFIG_QUEC_PROJECT_FEATURE_GPIO))
option(QL_APP_FEATURE_SPI4_EXT_NOR_SFFS  "Enable SPI4_EXT_NOR_SFFS" ON)
else()
message(STATUS "FEATURE SPI4_EXT_NOR_SFFS is disabled at core!")
option(QL_APP_FEATURE_SPI4_EXT_NOR_SFFS  "Enable SPI4_EXT_NOR_SFFS" OFF)
endif()
message(STATUS "QL_APP_FEATURE_SPI4_EXT_NOR_SFFS ${QL_APP_FEATURE_SPI4_EXT_NOR_SFFS}")
if(CONFIG_QUEC_PROJECT_FEATURE_SPI6_EXT_NOR)
option(QL_APP_FEATURE_SPI6_EXT_NOR  "Enable SPI6_EXT_NOR_FLASH" ON)
else()
message(STATUS "FEATURE SPI6_EXT_NOR_FLASH is disabled at core!")
option(QL_APP_FEATURE_SPI6_EXT_NOR  "Enable SPI6_EXT_NOR_FLASH" OFF)
endif()
message(STATUS "QL_APP_FEATURE_SPI6_EXT_NOR ${QL_APP_FEATURE_SPI6_EXT_NOR}")

if((CONFIG_QUEC_PROJECT_FEATURE_SPI_NAND_FLASH) AND (CONFIG_QUEC_PROJECT_FEATURE_GPIO))
option(QL_APP_FEATURE_SPI_NAND_FLASH  "Enable SPI_NAND_FLASH" ON)
else()
message(STATUS "FEATURE SPI_NAND_FLASH is disabled at core!")
option(QL_APP_FEATURE_SPI_NAND_FLASH  "Enable SPI_NAND_FLASH" OFF)
endif()
message(STATUS "QL_APP_FEATURE_SPI_NAND_FLASH ${QL_APP_FEATURE_SPI_NAND_FLASH}")

if(CONFIG_QUEC_PROJECT_FEATURE_UART)
option(QL_APP_FEATURE_UART  "Enable UART" ON)
else()
message(STATUS "FEATURE UART is disabled at core!")
option(QL_APP_FEATURE_UART  "Enable UART" OFF)
endif()
message(STATUS "QL_APP_FEATURE_UART ${QL_APP_FEATURE_UART}")

if(CONFIG_QUEC_PROJECT_FEATURE_UART)
option(QL_APP_FEATURE_RS485  "Enable RS485" ON)
else()
message(STATUS "FEATURE RS485 is disabled at core!")
option(QL_APP_FEATURE_RS485  "Enable RS485" OFF)
endif()
message(STATUS "QL_APP_FEATURE_RS485 ${QL_APP_FEATURE_RS485}")

if(CONFIG_QUEC_PROJECT_FEATURE_LEDCFG)
option(QL_APP_FEATURE_LEDCFG  "Enable LEDCFG" ON)
else()
message(STATUS "FEATURE LEDCFG is disabled at core!")
option(QL_APP_FEATURE_LEDCFG  "Enable LEDCFG" OFF)
endif()
message(STATUS "QL_APP_FEATURE_LEDCFG ${QL_APP_FEATURE_LEDCFG}")

if(CONFIG_QUEC_PROJECT_FEATURE_KEYPAD)
option(QL_APP_FEATURE_KEYPAD  "Enable KEYPAD" ON)
else()
message(STATUS "FEATURE KEYPAD is disabled at core!")
option(QL_APP_FEATURE_KEYPAD  "Enable KEYPAD" OFF)
endif()
message(STATUS "QL_APP_FEATURE_KEYPAD ${QL_APP_FEATURE_KEYPAD}")


option(QL_APP_FEATURE_DECODER  "Enable DECODER" ON)
message(STATUS "QL_APP_FEATURE_DECODER ${QL_APP_FEATURE_DECODER}")

if(CONFIG_QUEC_PROJECT_FEATURE_RTC)
option(QL_APP_FEATURE_RTC  "Enable RTC" ON)
else()
message(STATUS "FEATURE RTC is disabled at core!")
option(QL_APP_FEATURE_RTC  "Enable RTC" OFF)
endif()
message(STATUS "QL_APP_FEATURE_RTC ${QL_APP_FEATURE_RTC}")

if(CONFIG_QUEC_PROJECT_FEATURE_USB_CHARGE)
option(QL_APP_FEATURE_USB_CHARGE  "Enable USB CHARGE" ON)
else()
message(STATUS "FEATURE USB CHARGE is disabled at core!")
option(QL_APP_FEATURE_USB_CHARGE  "Enable USB CHARGE" OFF)
endif()
message(STATUS "QL_APP_FEATURE_USB_CHARGE ${QL_APP_FEATURE_USB_CHARGE}")

if(CONFIG_QUEC_PROJECT_FEATURE_VIRT_AT)
option(QL_APP_FEATURE_VIRT_AT  "Enable VIRT AT" ON)
else()
message(STATUS "FEATURE VIRT AT is disabled at core!")
option(QL_APP_FEATURE_VIRT_AT  "Enable VIRT_AT" OFF)
endif()
message(STATUS "QL_APP_FEATURE_VIRT_AT ${QL_APP_FEATURE_VIRT_AT}")

if(CONFIG_QUEC_PROJECT_FEATURE_FOTA)
option(QL_APP_FEATURE_FOTA  "Enable FOTA" ON)
else()
message(STATUS "FEATURE FOTA is disabled at core!")
option(QL_APP_FEATURE_FOTA  "Enable FOTA" OFF)
endif()

if(CONFIG_QUEC_PROJECT_FEATURE_I2C)
option(QL_APP_FEATURE_I2C  "Enable I2C" ON)
else()
message(STATUS "FEATURE I2C is disabled at core!")
option(QL_APP_FEATURE_I2C  "Enable I2C" OFF)
endif()
message(STATUS "QL_APP_FEATURE_I2C ${QL_APP_FEATURE_I2C}")

if(CONFIG_QUEC_PROJECT_FEATURE_USB)
option(QL_APP_FEATURE_USB  "Enable USB" ON)
else()
message(STATUS "FEATURE USB is disabled at core!")
option(QL_APP_FEATURE_USB  "Enable USB" OFF)
endif()
message(STATUS "QL_APP_FEATURE_USB ${QL_APP_FEATURE_USB}")

if(QL_APP_FEATURE_FOTA)

if (CONFIG_QUEC_PROJECT_FEATURE_HTTP)
option(QL_APP_FEATURE_HTTP_FOTA  "Enable FOTA_HTTP" ON)
else()
option(QL_APP_FEATURE_HTTP_FOTA  "Enable FOTA_HTTP" OFF)
endif()
message(STATUS "QL_APP_FEATURE_HTTP_FOTA ${QL_APP_FEATURE_HTTP_FOTA}")

if (CONFIG_QUEC_PROJECT_FEATURE_FTP)
option(QL_APP_FEATURE_FTP_FOTA  "Enable FOTA_FTP" ON)
else()
option(QL_APP_FEATURE_FTP_FOTA  "Enable FOTA_FTP" OFF)
endif()
message(STATUS "QL_APP_FEATURE_FTP_FOTA ${QL_APP_FEATURE_FTP_FOTA}")

endif()

if(CONFIG_QUEC_PROJECT_FEATURE_JAMDET)
option(QL_APP_FEATURE_JAMDET  "Enable JAMDET" ON)
else()
message(STATUS "FEATURE JAMDET is disabled at core!")
option(QL_APP_FEATURE_JAMDET  "Enable JAMDET" OFF)
endif()
message(STATUS "QL_APP_FEATURE_JAMDET ${QL_APP_FEATURE_JAMDET}")

if(CONFIG_QUEC_PROJECT_FEATURE_SDMMC)
option(QL_APP_FEATURE_SDMMC  "Enable SDMMC" ON)
else()
message(STATUS "FEATURE SDMMC is disabled at core!")
option(QL_APP_FEATURE_SDMMC  "Enable SDMMC" OFF)
endif()
message(STATUS "QL_APP_FEATURE_SDMMC ${QL_APP_FEATURE_SDMMC}")

if(CONFIG_QUEC_PROJECT_FEATURE_PWK)
option(QL_APP_FEATURE_PWK  "Enable PWK" ON)
else()
message(STATUS "FEATURE PWK is disabled at core!")
option(QL_APP_FEATURE_PWK  "Enable PWK" OFF)
endif()
message(STATUS "QL_APP_FEATURE_PWK ${QL_APP_FEATURE_PWK}")

if(CONFIG_QUEC_PROJECT_FEATURE_USBNET)
option(QL_APP_FEATURE_USBNET  "Enable USBNET" ON)
else()
message(STATUS "FEATURE USBNET is disabled at core!")
option(QL_APP_FEATURE_USBNET  "Enable USBNET" OFF)
endif()
message(STATUS "QL_APP_FEATURE_USBNET ${QL_APP_FEATURE_USBNET}")

if(CONFIG_QUEC_PROJECT_FEATURE_FS_NAND_FLASH)
option(QL_APP_FEATURE_FS_NAND_FLASH  "Enable FS_NAND_FLASH" ON)
else()
message(STATUS "FEATURE FS_NAND_FLASH is disabled at core!")
option(QL_APP_FEATURE_FS_NAND_FLASH  "Enable FS_NAND_FLASH" OFF)
endif()
message(STATUS "QL_APP_FEATURE_FS_NAND_FLASH ${QL_APP_FEATURE_FS_NAND_FLASH}")

if((CONFIG_QUEC_PROJECT_FEATURE_SSH2) AND (CONFIG_QUEC_PROJECT_FEATURE_FTP))
option(QL_APP_FEATURE_SFTP  "Enable SFTP" ON)
else()
message(STATUS "FEATURE SFTP is disabled at core!")
option(QL_APP_FEATURE_SFTP  "Enable SFTP" OFF)
endif()
message(STATUS "QL_APP_FEATURE_SFTP ${QL_APP_FEATURE_SFTP}")

option(QL_APP_FEATURE_MXML  "Enable MXML" ON)
message(STATUS "QL_APP_FEATURE_MXML ${QL_APP_FEATURE_MXML}")

if(CONFIG_QUEC_PROJECT_FEATURE_EMBED_NOR_FLASH)
option(QL_APP_FEATURE_EMBED_NOR_FLASH  "Enable EMBED NOR FLASH" ON)
else()
message(STATUS "FEATURE EMBED NOR FLASH is disabled at core!")
option(QL_APP_FEATURE_EMBED_NOR_FLASH  "Disable EMBED NOR FLASH" OFF)
endif()
message(STATUS "QL_APP_FEATURE_EMBED_NOR_FLASH ${QL_APP_FEATURE_EMBED_NOR_FLASH}")

if((CONFIG_QUEC_PROJECT_FEATURE_FOTA) AND (CONFIG_QUEC_PROJECT_FEATURE_HTTP) AND (CONFIG_QUEC_PROJECT_FEATURE_CLOUDOTA))
option(QL_APP_FEATURE_CLOUDOTA  "Enable CLOUDOTA" ON)
else()
message(STATUS "FEATURE CLOUDOTA is disabled at core!")
option(QL_APP_FEATURE_CLOUDOTA  "Enable CLOUDOTA" OFF)
endif()
message(STATUS "QL_APP_FEATURE_CLOUDOTA ${QL_APP_FEATURE_CLOUDOTA}")

option(QL_APP_FEATURE_HILINK  "Enable HILINK" ON)
message(STATUS "QL_APP_FEATURE_HILINK ${QL_APP_FEATURE_HILINK}")

if(CONFIG_QUEC_PROJECT_FEATURE_GPRS_DATA_TRANSFER)
option(QL_APP_FEATURE_GPRS_DATA_TRANSFER  "Enable GPRS_DATA_TRANSFER" ON)
else()
message(STATUS "FEATURE GPRS_DATA_TRANSFER is disabled at core!")
option(QL_APP_FEATURE_GPRS_DATA_TRANSFER  "Enable GPRS_DATA_TRANSFER" OFF)
endif()
message(STATUS "QL_APP_FEATURE_GPRS_DATA_TRANSFER ${QL_APP_FEATURE_GPRS_DATA_TRANSFER}")

if(CONFIG_QUEC_PROJECT_FEATURE_PSM)
option(QL_APP_FEATURE_PSM  "Enable PSM" ON)
else()
message(STATUS "FEATURE PSM is disabled at core!")
option(QL_APP_FEATURE_PSM  "Enable PSM" OFF)
endif()
message(STATUS "QL_APP_FEATURE_PSM ${QL_APP_FEATURE_PSM}")

if(CONFIG_QUEC_PROJECT_FEATURE_TP)
option(QL_APP_FEATURE_TP  "Enable TP" ON)
else()
message(STATUS "FEATURE TP  is disabled at core!")
option(QL_APP_FEATURE_TP   "Enable TP " OFF)
endif()
message(STATUS "QL_APP_FEATURE_TP  ${QL_APP_FEATURE_TP}")

################################################################################################################
# Quectel open sdk package config
################################################################################################################
if (QL_APP_FEATURE_GNSS)
option(QL_APP_PACK_FILE "Enable pack file to firmware package" ON)
elseif (QL_APP_FEATURE_TTS)
option(QL_APP_PACK_FILE "Enable pack file to firmware package" ON)
else()
option(QL_APP_PACK_FILE "Enable pack file to firmware package" OFF)
endif()

if(CONFIG_QUEC_PROJECT_FEATURE_TTS)
option(QL_APP_FEATURE_TTS  "Enable TTS" ON)
else()
message(STATUS "FEATURE TTS at core is ${CONFIG_QUEC_PROJECT_FEATURE_TTS}")
option(QL_APP_FEATURE_TTS  "Enable TTS" OFF)
endif()
message(STATUS "QL_APP_FEATURE_TTS ${QL_APP_FEATURE_TTS}")

if (QL_APP_PACK_FILE)
if(QL_APP_FEATURE_GNSS)
if(QL_APP_FEATURE_TTS)
set(QL_APP_PACK_FILE_JSON_PATH components/ql-config/download/prepack/ql_prepack_tts_gps.json)
else()
set(QL_APP_PACK_FILE_JSON_PATH components/ql-config/download/prepack/ql_prepack_gps.json)
endif()
elseif(QL_APP_FEATURE_TTS)
set(QL_APP_PACK_FILE_JSON_PATH components/ql-config/download/prepack/ql_prepack_tts.json)
endif()
endif()

file(WRITE ${BINARY_TOP_DIR}/ql_prepack.opt "Y")
message(STATUS "QL_APP_PACK_FILE ${QL_APP_PACK_FILE} @ ${QL_APP_PACK_FILE_JSON_PATH}")

if (QL_APP_FEATURE_SECURE_BOOT)
file(WRITE ${BINARY_TOP_DIR}/ql_secure.opt "Y")
endif()
message("\n")

endif()
