/****************************************************************
**                                                              *
**  FILE         :  UN_GBTB.C                                   *
****************************************************************/

#include "yx_system.h"
#include "yx_structs.h"
#include "un_gbtb.h"

static INT16U const UN_GBTB[] = 
        {
          0x00A4,   0xA1E8,
          0x00A7,   0xA1EC,
          0x00A8,   0xA1A7,
          0x00B0,   0xA1E3,
          0x00B1,   0xA1C0,
          0x00D7,   0xA1C1,
          0x00E0,   0xA8A4,
          0x00E1,   0xA8A2,
          0x00E8,   0xA8A8,
          0x00E9,   0xA8A6,
          0x00EA,   0xA8BA,
          0x00EC,   0xA8AC,
          0x00ED,   0xA8AA,
          0x00F2,   0xA8B0,
          0x00F3,   0xA8AE,
          0x00F7,   0xA1C2,
          0x00F9,   0xA8B4,
          0x00FA,   0xA8B2,
          0x00FC,   0xA8B9,
          0x0101,   0xA8A1,
          0x0113,   0xA8A5,
          0x011B,   0xA8A7,
          0x012B,   0xA8A9,
          0x014D,   0xA8AD,
          0x016B,   0xA8B1,
          0x01CE,   0xA8A3,
          0x01D0,   0xA8AB,
          0x01D2,   0xA8AF,
          0x01D4,   0xA8B3,
          0x01D6,   0xA8B5,
          0x01D8,   0xA8B6,
          0x01DA,   0xA8B7,
          0x01DC,   0xA8B8,
          0x02C7,   0xA1A6,
          0x02C9,   0xA1A5,
          0x0391,   0xA6A1,
          0x0392,   0xA6A2,
          0x0393,   0xA6A3,
          0x0394,   0xA6A4,
          0x0395,   0xA6A5,
          0x0396,   0xA6A6,
          0x0397,   0xA6A7,
          0x0398,   0xA6A8,
          0x0399,   0xA6A9,
          0x039A,   0xA6AA,
          0x039B,   0xA6AB,
          0x039C,   0xA6AC,
          0x039D,   0xA6AD,
          0x039E,   0xA6AE,
          0x039F,   0xA6AF,
          0x03A0,   0xA6B0,
          0x03A1,   0xA6B1,
          0x03A3,   0xA6B2,
          0x03A4,   0xA6B3,
          0x03A5,   0xA6B4,
          0x03A6,   0xA6B5,
          0x03A7,   0xA6B6,
          0x03A8,   0xA6B7,
          0x03A9,   0xA6B8,
          0x03B1,   0xA6C1,
          0x03B2,   0xA6C2,
          0x03B3,   0xA6C3,
          0x03B4,   0xA6C4,
          0x03B5,   0xA6C5,
          0x03B6,   0xA6C6,
          0x03B7,   0xA6C7,
          0x03B8,   0xA6C8,
          0x03B9,   0xA6C9,
          0x03BA,   0xA6CA,
          0x03BB,   0xA6CB,
          0x03BC,   0xA6CC,
          0x03BD,   0xA6CD,
          0x03BE,   0xA6CE,
          0x03BF,   0xA6CF,
          0x03C0,   0xA6D0,
          0x03C1,   0xA6D1,
          0x03C3,   0xA6D2,
          0x03C4,   0xA6D3,
          0x03C5,   0xA6D4,
          0x03C6,   0xA6D5,
          0x03C7,   0xA6D6,
          0x03C8,   0xA6D7,
          0x03C9,   0xA6D8,
          0x0401,   0xA7A7,
          0x0410,   0xA7A1,
          0x0411,   0xA7A2,
          0x0412,   0xA7A3,
          0x0413,   0xA7A4,
          0x0414,   0xA7A5,
          0x0415,   0xA7A6,
          0x0416,   0xA7A8,
          0x0417,   0xA7A9,
          0x0418,   0xA7AA,
          0x0419,   0xA7AB,
          0x041A,   0xA7AC,
          0x041B,   0xA7AD,
          0x041C,   0xA7AE,
          0x041D,   0xA7AF,
          0x041E,   0xA7B0,
          0x041F,   0xA7B1,
          0x0420,   0xA7B2,
          0x0421,   0xA7B3,
          0x0422,   0xA7B4,
          0x0423,   0xA7B5,
          0x0424,   0xA7B6,
          0x0425,   0xA7B7,
          0x0426,   0xA7B8,
          0x0427,   0xA7B9,
          0x0428,   0xA7BA,
          0x0429,   0xA7BB,
          0x042A,   0xA7BC,
          0x042B,   0xA7BD,
          0x042C,   0xA7BE,
          0x042D,   0xA7BF,
          0x042E,   0xA7C0,
          0x042F,   0xA7C1,
          0x0430,   0xA7D1,
          0x0431,   0xA7D2,
          0x0432,   0xA7D3,
          0x0433,   0xA7D4,
          0x0434,   0xA7D5,
          0x0435,   0xA7D6,
          0x0436,   0xA7D8,
          0x0437,   0xA7D9,
          0x0438,   0xA7DA,
          0x0439,   0xA7DB,
          0x043A,   0xA7DC,
          0x043B,   0xA7DD,
          0x043C,   0xA7DE,
          0x043D,   0xA7DF,
          0x043E,   0xA7E0,
          0x043F,   0xA7E1,
          0x0440,   0xA7E2,
          0x0441,   0xA7E3,
          0x0442,   0xA7E4,
          0x0443,   0xA7E5,
          0x0444,   0xA7E6,
          0x0445,   0xA7E7,
          0x0446,   0xA7E8,
          0x0447,   0xA7E9,
          0x0448,   0xA7EA,
          0x0449,   0xA7EB,
          0x044A,   0xA7EC,
          0x044B,   0xA7ED,
          0x044C,   0xA7EE,
          0x044D,   0xA7EF,
          0x044E,   0xA7F0,
          0x044F,   0xA7F1,
          0x0451,   0xA7D7,
          0x2015,   0xA1AA,
          0x2016,   0xA1AC,
          0x2018,   0xA1AE,
          0x2019,   0xA1AF,
          0x201C,   0xA1B0,
          0x201D,   0xA1B1,
          0x2026,   0xA1AD,
          0x2030,   0xA1EB,
          0x2032,   0xA1E4,
          0x2033,   0xA1E5,
          0x203B,   0xA1F9,
          0x2103,   0xA1E6,
          0x2116,   0xA1ED,
          0x2160,   0xA2F1,
          0x2161,   0xA2F2,
          0x2162,   0xA2F3,
          0x2163,   0xA2F4,
          0x2164,   0xA2F5,
          0x2165,   0xA2F6,
          0x2166,   0xA2F7,
          0x2167,   0xA2F8,
          0x2168,   0xA2F9,
          0x2169,   0xA2FA,
          0x216A,   0xA2FB,
          0x216B,   0xA2FC,
          0x2190,   0xA1FB,
          0x2191,   0xA1FC,
          0x2192,   0xA1FA,
          0x2193,   0xA1FD,
          0x2208,   0xA1CA,
          0x220F,   0xA1C7,
          0x2211,   0xA1C6,
          0x221A,   0xA1CC,
          0x221D,   0xA1D8,
          0x221E,   0xA1DE,
          0x2220,   0xA1CF,
          0x2225,   0xA1CE,
          0x2227,   0xA1C4,
          0x2228,   0xA1C5,
          0x2229,   0xA1C9,
          0x222A,   0xA1C8,
          0x222B,   0xA1D2,
          0x222E,   0xA1D3,
          0x2234,   0xA1E0,
          0x2235,   0xA1DF,
          0x2236,   0xA1C3,
          0x2237,   0xA1CB,
          0x223D,   0xA1D7,
          0x2248,   0xA1D6,
          0x224C,   0xA1D5,
          0x2260,   0xA1D9,
          0x2261,   0xA1D4,
          0x2264,   0xA1DC,
          0x2265,   0xA1DD,
          0x226E,   0xA1DA,
          0x226F,   0xA1DB,
          0x2299,   0xA1D1,
          0x22A5,   0xA1CD,
          0x2312,   0xA1D0,
          0x2460,   0xA2D9,
          0x2461,   0xA2DA,
          0x2462,   0xA2DB,
          0x2463,   0xA2DC,
          0x2464,   0xA2DD,
          0x2465,   0xA2DE,
          0x2466,   0xA2DF,
          0x2467,   0xA2E0,
          0x2468,   0xA2E1,
          0x2469,   0xA2E2,
          0x2474,   0xA2C5,
          0x2475,   0xA2C6,
          0x2476,   0xA2C7,
          0x2477,   0xA2C8,
          0x2478,   0xA2C9,
          0x2479,   0xA2CA,
          0x247A,   0xA2CB,
          0x247B,   0xA2CC,
          0x247C,   0xA2CD,
          0x247D,   0xA2CE,
          0x247E,   0xA2CF,
          0x247F,   0xA2D0,
          0x2480,   0xA2D1,
          0x2481,   0xA2D2,
          0x2482,   0xA2D3,
          0x2483,   0xA2D4,
          0x2484,   0xA2D5,
          0x2485,   0xA2D6,
          0x2486,   0xA2D7,
          0x2487,   0xA2D8,
          0x2488,   0xA2B1,
          0x2489,   0xA2B2,
          0x248A,   0xA2B3,
          0x248B,   0xA2B4,
          0x248C,   0xA2B5,
          0x248D,   0xA2B6,
          0x248E,   0xA2B7,
          0x248F,   0xA2B8,
          0x2490,   0xA2B9,
          0x2491,   0xA2BA,
          0x2492,   0xA2BB,
          0x2493,   0xA2BC,
          0x2494,   0xA2BD,
          0x2495,   0xA2BE,
          0x2496,   0xA2BF,
          0x2497,   0xA2C0,
          0x2498,   0xA2C1,
          0x2499,   0xA2C2,
          0x249A,   0xA2C3,
          0x249B,   0xA2C4,
          0x2500,   0xA9A4,
          0x2501,   0xA9A5,
          0x2502,   0xA9A6,
          0x2503,   0xA9A7,
          0x2504,   0xA9A8,
          0x2505,   0xA9A9,
          0x2506,   0xA9AA,
          0x2507,   0xA9AB,
          0x2508,   0xA9AC,
          0x2509,   0xA9AD,
          0x250A,   0xA9AE,
          0x250B,   0xA9AF,
          0x250C,   0xA9B0,
          0x250D,   0xA9B1,
          0x250E,   0xA9B2,
          0x250F,   0xA9B3,
          0x2510,   0xA9B4,
          0x2511,   0xA9B5,
          0x2512,   0xA9B6,
          0x2513,   0xA9B7,
          0x2514,   0xA9B8,
          0x2515,   0xA9B9,
          0x2516,   0xA9BA,
          0x2517,   0xA9BB,
          0x2518,   0xA9BC,
          0x2519,   0xA9BD,
          0x251A,   0xA9BE,
          0x251B,   0xA9BF,
          0x251C,   0xA9C0,
          0x251D,   0xA9C1,
          0x251E,   0xA9C2,
          0x251F,   0xA9C3,
          0x2520,   0xA9C4,
          0x2521,   0xA9C5,
          0x2522,   0xA9C6,
          0x2523,   0xA9C7,
          0x2524,   0xA9C8,
          0x2525,   0xA9C9,
          0x2526,   0xA9CA,
          0x2527,   0xA9CB,
          0x2528,   0xA9CC,
          0x2529,   0xA9CD,
          0x252A,   0xA9CE,
          0x252B,   0xA9CF,
          0x252C,   0xA9D0,
          0x252D,   0xA9D1,
          0x252E,   0xA9D2,
          0x252F,   0xA9D3,
          0x2530,   0xA9D4,
          0x2531,   0xA9D5,
          0x2532,   0xA9D6,
          0x2533,   0xA9D7,
          0x2534,   0xA9D8,
          0x2535,   0xA9D9,
          0x2536,   0xA9DA,
          0x2537,   0xA9DB,
          0x2538,   0xA9DC,
          0x2539,   0xA9DD,
          0x253A,   0xA9DE,
          0x253B,   0xA9DF,
          0x253C,   0xA9E0,
          0x253D,   0xA9E1,
          0x253E,   0xA9E2,
          0x253F,   0xA9E3,
          0x2540,   0xA9E4,
          0x2541,   0xA9E5,
          0x2542,   0xA9E6,
          0x2543,   0xA9E7,
          0x2544,   0xA9E8,
          0x2545,   0xA9E9,
          0x2546,   0xA9EA,
          0x2547,   0xA9EB,
          0x2548,   0xA9EC,
          0x2549,   0xA9ED,
          0x254A,   0xA9EE,
          0x254B,   0xA9EF,
          0x25A0,   0xA1F6,
          0x25A1,   0xA1F5,
          0x25B2,   0xA1F8,
          0x25B3,   0xA1F7,
          0x25C6,   0xA1F4,
          0x25C7,   0xA1F3,
          0x25CB,   0xA1F0,
          0x25CE,   0xA1F2,
          0x25CF,   0xA1F1,
          0x2605,   0xA1EF,
          0x2606,   0xA1EE,
          0x2640,   0xA1E2,
          0x2642,   0xA1E1,
          0x3000,   0xA1A1,
          0x3001,   0xA1A2,
          0x3002,   0xA1A3,
          0x3003,   0xA1A8,
          0x3005,   0xA1A9,
          0x3008,   0xA1B4,
          0x3009,   0xA1B5,
          0x300A,   0xA1B6,
          0x300B,   0xA1B7,
          0x300C,   0xA1B8,
          0x300D,   0xA1B9,
          0x300E,   0xA1BA,
          0x300F,   0xA1BB,
          0x3010,   0xA1BE,
          0x3011,   0xA1BF,
          0x3013,   0xA1FE,
          0x3014,   0xA1B2,
          0x3015,   0xA1B3,
          0x3016,   0xA1BC,
          0x3017,   0xA1BD,
          0x3041,   0xA4A1,
          0x3042,   0xA4A2,
          0x3043,   0xA4A3,
          0x3044,   0xA4A4,
          0x3045,   0xA4A5,
          0x3046,   0xA4A6,
          0x3047,   0xA4A7,
          0x3048,   0xA4A8,
          0x3049,   0xA4A9,
          0x304A,   0xA4AA,
          0x304B,   0xA4AB,
          0x304C,   0xA4AC,
          0x304D,   0xA4AD,
          0x304E,   0xA4AE,
          0x304F,   0xA4AF,
          0x3050,   0xA4B0,
          0x3051,   0xA4B1,
          0x3052,   0xA4B2,
          0x3053,   0xA4B3,
          0x3054,   0xA4B4,
          0x3055,   0xA4B5,
          0x3056,   0xA4B6,
          0x3057,   0xA4B7,
          0x3058,   0xA4B8,
          0x3059,   0xA4B9,
          0x305A,   0xA4BA,
          0x305B,   0xA4BB,
          0x305C,   0xA4BC,
          0x305D,   0xA4BD,
          0x305E,   0xA4BE,
          0x305F,   0xA4BF,
          0x3060,   0xA4C0,
          0x3061,   0xA4C1,
          0x3062,   0xA4C2,
          0x3063,   0xA4C3,
          0x3064,   0xA4C4,
          0x3065,   0xA4C5,
          0x3066,   0xA4C6,
          0x3067,   0xA4C7,
          0x3068,   0xA4C8,
          0x3069,   0xA4C9,
          0x306A,   0xA4CA,
          0x306B,   0xA4CB,
          0x306C,   0xA4CC,
          0x306D,   0xA4CD,
          0x306E,   0xA4CE,
          0x306F,   0xA4CF,
          0x3070,   0xA4D0,
          0x3071,   0xA4D1,
          0x3072,   0xA4D2,
          0x3073,   0xA4D3,
          0x3074,   0xA4D4,
          0x3075,   0xA4D5,
          0x3076,   0xA4D6,
          0x3077,   0xA4D7,
          0x3078,   0xA4D8,
          0x3079,   0xA4D9,
          0x307A,   0xA4DA,
          0x307B,   0xA4DB,
          0x307C,   0xA4DC,
          0x307D,   0xA4DD,
          0x307E,   0xA4DE,
          0x307F,   0xA4DF,
          0x3080,   0xA4E0,
          0x3081,   0xA4E1,
          0x3082,   0xA4E2,
          0x3083,   0xA4E3,
          0x3084,   0xA4E4,
          0x3085,   0xA4E5,
          0x3086,   0xA4E6,
          0x3087,   0xA4E7,
          0x3088,   0xA4E8,
          0x3089,   0xA4E9,
          0x308A,   0xA4EA,
          0x308B,   0xA4EB,
          0x308C,   0xA4EC,
          0x308D,   0xA4ED,
          0x308E,   0xA4EE,
          0x308F,   0xA4EF,
          0x3090,   0xA4F0,
          0x3091,   0xA4F1,
          0x3092,   0xA4F2,
          0x3093,   0xA4F3,
          0x30A1,   0xA5A1,
          0x30A2,   0xA5A2,
          0x30A3,   0xA5A3,
          0x30A4,   0xA5A4,
          0x30A5,   0xA5A5,
          0x30A6,   0xA5A6,
          0x30A7,   0xA5A7,
          0x30A8,   0xA5A8,
          0x30A9,   0xA5A9,
          0x30AA,   0xA5AA,
          0x30AB,   0xA5AB,
          0x30AC,   0xA5AC,
          0x30AD,   0xA5AD,
          0x30AE,   0xA5AE,
          0x30AF,   0xA5AF,
          0x30B0,   0xA5B0,
          0x30B1,   0xA5B1,
          0x30B2,   0xA5B2,
          0x30B3,   0xA5B3,
          0x30B4,   0xA5B4,
          0x30B5,   0xA5B5,
          0x30B6,   0xA5B6,
          0x30B7,   0xA5B7,
          0x30B8,   0xA5B8,
          0x30B9,   0xA5B9,
          0x30BA,   0xA5BA,
          0x30BB,   0xA5BB,
          0x30BC,   0xA5BC,
          0x30BD,   0xA5BD,
          0x30BE,   0xA5BE,
          0x30BF,   0xA5BF,
          0x30C0,   0xA5C0,
          0x30C1,   0xA5C1,
          0x30C2,   0xA5C2,
          0x30C3,   0xA5C3,
          0x30C4,   0xA5C4,
          0x30C5,   0xA5C5,
          0x30C6,   0xA5C6,
          0x30C7,   0xA5C7,
          0x30C8,   0xA5C8,
          0x30C9,   0xA5C9,
          0x30CA,   0xA5CA,
          0x30CB,   0xA5CB,
          0x30CC,   0xA5CC,
          0x30CD,   0xA5CD,
          0x30CE,   0xA5CE,
          0x30CF,   0xA5CF,
          0x30D0,   0xA5D0,
          0x30D1,   0xA5D1,
          0x30D2,   0xA5D2,
          0x30D3,   0xA5D3,
          0x30D4,   0xA5D4,
          0x30D5,   0xA5D5,
          0x30D6,   0xA5D6,
          0x30D7,   0xA5D7,
          0x30D8,   0xA5D8,
          0x30D9,   0xA5D9,
          0x30DA,   0xA5DA,
          0x30DB,   0xA5DB,
          0x30DC,   0xA5DC,
          0x30DD,   0xA5DD,
          0x30DE,   0xA5DE,
          0x30DF,   0xA5DF,
          0x30E0,   0xA5E0,
          0x30E1,   0xA5E1,
          0x30E2,   0xA5E2,
          0x30E3,   0xA5E3,
          0x30E4,   0xA5E4,
          0x30E5,   0xA5E5,
          0x30E6,   0xA5E6,
          0x30E7,   0xA5E7,
          0x30E8,   0xA5E8,
          0x30E9,   0xA5E9,
          0x30EA,   0xA5EA,
          0x30EB,   0xA5EB,
          0x30EC,   0xA5EC,
          0x30ED,   0xA5ED,
          0x30EE,   0xA5EE,
          0x30EF,   0xA5EF,
          0x30F0,   0xA5F0,
          0x30F1,   0xA5F1,
          0x30F2,   0xA5F2,
          0x30F3,   0xA5F3,
          0x30F4,   0xA5F4,
          0x30F5,   0xA5F5,
          0x30F6,   0xA5F6,
          0x30FB,   0xA1A4,
          0x3105,   0xA8C5,
          0x3106,   0xA8C6,
          0x3107,   0xA8C7,
          0x3108,   0xA8C8,
          0x3109,   0xA8C9,
          0x310A,   0xA8CA,
          0x310B,   0xA8CB,
          0x310C,   0xA8CC,
          0x310D,   0xA8CD,
          0x310E,   0xA8CE,
          0x310F,   0xA8CF,
          0x3110,   0xA8D0,
          0x3111,   0xA8D1,
          0x3112,   0xA8D2,
          0x3113,   0xA8D3,
          0x3114,   0xA8D4,
          0x3115,   0xA8D5,
          0x3116,   0xA8D6,
          0x3117,   0xA8D7,
          0x3118,   0xA8D8,
          0x3119,   0xA8D9,
          0x311A,   0xA8DA,
          0x311B,   0xA8DB,
          0x311C,   0xA8DC,
          0x311D,   0xA8DD,
          0x311E,   0xA8DE,
          0x311F,   0xA8DF,
          0x3120,   0xA8E0,
          0x3121,   0xA8E1,
          0x3122,   0xA8E2,
          0x3123,   0xA8E3,
          0x3124,   0xA8E4,
          0x3125,   0xA8E5,
          0x3126,   0xA8E6,
          0x3127,   0xA8E7,
          0x3128,   0xA8E8,
          0x3129,   0xA8E9,
          0x3220,   0xA2E5,
          0x3221,   0xA2E6,
          0x3222,   0xA2E7,
          0x3223,   0xA2E8,
          0x3224,   0xA2E9,
          0x3225,   0xA2EA,
          0x3226,   0xA2EB,
          0x3227,   0xA2EC,
          0x3228,   0xA2ED,
          0x3229,   0xA2EE,
          0x4E00,   0xD2BB,
          0x4E01,   0xB6A1,
          0x4E03,   0xC6DF,
          0x4E07,   0xCDF2,
          0x4E08,   0xD5C9,
          0x4E09,   0xC8FD,
          0x4E0A,   0xC9CF,
          0x4E0B,   0xCFC2,
          0x4E0C,   0xD8A2,
          0x4E0D,   0xB2BB,
          0x4E0E,   0xD3EB,
          0x4E10,   0xD8A4,
          0x4E11,   0xB3F3,
          0x4E13,   0xD7A8,
          0x4E14,   0xC7D2,
          0x4E15,   0xD8A7,
          0x4E16,   0xCAC0,
          0x4E18,   0xC7F0,
          0x4E19,   0xB1FB,
          0x4E1A,   0xD2B5,
          0x4E1B,   0xB4D4,
          0x4E1C,   0xB6AB,
          0x4E1D,   0xCBBF,
          0x4E1E,   0xD8A9,
          0x4E22,   0xB6AA,
          0x4E24,   0xC1BD,
          0x4E25,   0xD1CF,
          0x4E27,   0xC9A5,
          0x4E28,   0xD8AD,
          0x4E2A,   0xB8F6,
          0x4E2B,   0xD1BE,
          0x4E2C,   0xE3DC,
          0x4E2D,   0xD6D0,
          0x4E30,   0xB7E1,
          0x4E32,   0xB4AE,
          0x4E34,   0xC1D9,
          0x4E36,   0xD8BC,
          0x4E38,   0xCDE8,
          0x4E39,   0xB5A4,
          0x4E3A,   0xCEAA,
          0x4E3B,   0xD6F7,
          0x4E3D,   0xC0F6,
          0x4E3E,   0xBED9,
          0x4E3F,   0xD8AF,
          0x4E43,   0xC4CB,
          0x4E45,   0xBEC3,
          0x4E47,   0xD8B1,
          0x4E48,   0xC3B4,
          0x4E49,   0xD2E5,
          0x4E4B,   0xD6AE,
          0x4E4C,   0xCEDA,
          0x4E4D,   0xD5A7,
          0x4E4E,   0xBAF5,
          0x4E4F,   0xB7A6,
          0x4E50,   0xC0D6,
          0x4E52,   0xC6B9,
          0x4E53,   0xC5D2,
          0x4E54,   0xC7C7,
          0x4E56,   0xB9D4,
          0x4E58,   0xB3CB,
          0x4E59,   0xD2D2,
          0x4E5C,   0xD8BF,
          0x4E5D,   0xBEC5,
          0x4E5E,   0xC6F2,
          0x4E5F,   0xD2B2,
          0x4E60,   0xCFB0,
          0x4E61,   0xCFE7,
          0x4E66,   0xCAE9,
          0x4E69,   0xD8C0,
          0x4E70,   0xC2F2,
          0x4E71,   0xC2D2,
          0x4E73,   0xC8E9,
          0x4E7E,   0xC7AC,
          0x4E86,   0xC1CB,
          0x4E88,   0xD3E8,
          0x4E89,   0xD5F9,
          0x4E8B,   0xCAC2,
          0x4E8C,   0xB6FE,
          0x4E8D,   0xD8A1,
          0x4E8E,   0xD3DA,
          0x4E8F,   0xBFF7,
          0x4E91,   0xD4C6,
          0x4E92,   0xBBA5,
          0x4E93,   0xD8C1,
          0x4E94,   0xCEE5,
          0x4E95,   0xBEAE,
          0x4E98,   0xD8A8,
          0x4E9A,   0xD1C7,
          0x4E9B,   0xD0A9,
          0x4E9F,   0xD8BD,
          0x4EA0,   0xD9EF,
          0x4EA1,   0xCDF6,
          0x4EA2,   0xBFBA,
          0x4EA4,   0xBDBB,
          0x4EA5,   0xBAA5,
          0x4EA6,   0xD2E0,
          0x4EA7,   0xB2FA,
          0x4EA8,   0xBAE0,
          0x4EA9,   0xC4B6,
          0x4EAB,   0xCFED,
          0x4EAC,   0xBEA9,
          0x4EAD,   0xCDA4,
          0x4EAE,   0xC1C1,
          0x4EB2,   0xC7D7,
          0x4EB3,   0xD9F1,
          0x4EB5,   0xD9F4,
          0x4EBA,   0xC8CB,
          0x4EBB,   0xD8E9,
          0x4EBF,   0xD2DA,
          0x4EC0,   0xCAB2,
          0x4EC1,   0xC8CA,
          0x4EC2,   0xD8EC,
          0x4EC3,   0xD8EA,
          0x4EC4,   0xD8C6,
          0x4EC5,   0xBDF6,
          0x4EC6,   0xC6CD,
          0x4EC7,   0xB3F0,
          0x4EC9,   0xD8EB,
          0x4ECA,   0xBDF1,
          0x4ECB,   0xBDE9,
          0x4ECD,   0xC8D4,
          0x4ECE,   0xB4D3,
          0x4ED1,   0xC2D8,
          0x4ED3,   0xB2D6,
          0x4ED4,   0xD7D0,
          0x4ED5,   0xCACB,
          0x4ED6,   0xCBFB,
          0x4ED7,   0xD5CC,
          0x4ED8,   0xB8B6,
          0x4ED9,   0xCFC9,
          0x4EDD,   0xD9DA,
          0x4EDE,   0xD8F0,
          0x4EDF,   0xC7AA,
          0x4EE1,   0xD8EE,
          0x4EE3,   0xB4FA,
          0x4EE4,   0xC1EE,
          0x4EE5,   0xD2D4,
          0x4EE8,   0xD8ED,
          0x4EEA,   0xD2C7,
          0x4EEB,   0xD8EF,
          0x4EEC,   0xC3C7,
          0x4EF0,   0xD1F6,
          0x4EF2,   0xD6D9,
          0x4EF3,   0xD8F2,
          0x4EF5,   0xD8F5,
          0x4EF6,   0xBCFE,
          0x4EF7,   0xBCDB,
          0x4EFB,   0xC8CE,
          0x4EFD,   0xB7DD,
          0x4EFF,   0xB7C2,
          0x4F01,   0xC6F3,
          0x4F09,   0xD8F8,
          0x4F0A,   0xD2C1,
          0x4F0D,   0xCEE9,
          0x4F0E,   0xBCBF,
          0x4F0F,   0xB7FC,
          0x4F10,   0xB7A5,
          0x4F11,   0xD0DD,
          0x4F17,   0xD6DA,
          0x4F18,   0xD3C5,
          0x4F19,   0xBBEF,
          0x4F1A,   0xBBE1,
          0x4F1B,   0xD8F1,
          0x4F1E,   0xC9A1,
          0x4F1F,   0xCEB0,
          0x4F20,   0xB4AB,
          0x4F22,   0xD8F3,
          0x4F24,   0xC9CB,
          0x4F25,   0xD8F6,
          0x4F26,   0xC2D7,
          0x4F27,   0xD8F7,
          0x4F2A,   0xCEB1,
          0x4F2B,   0xD8F9,
          0x4F2F,   0xB2AE,
          0x4F30,   0xB9C0,
          0x4F32,   0xD9A3,
          0x4F34,   0xB0E9,
          0x4F36,   0xC1E6,
          0x4F38,   0xC9EC,
          0x4F3A,   0xCBC5,
          0x4F3C,   0xCBC6,
          0x4F3D,   0xD9A4,
          0x4F43,   0xB5E8,
          0x4F46,   0xB5AB,
          0x4F4D,   0xCEBB,
          0x4F4E,   0xB5CD,
          0x4F4F,   0xD7A1,
          0x4F50,   0xD7F4,
          0x4F51,   0xD3D3,
          0x4F53,   0xCCE5,
          0x4F55,   0xBACE,
          0x4F57,   0xD9A2,
          0x4F58,   0xD9DC,
          0x4F59,   0xD3E0,
          0x4F5A,   0xD8FD,
          0x4F5B,   0xB7F0,
          0x4F5C,   0xD7F7,
          0x4F5D,   0xD8FE,
          0x4F5E,   0xD8FA,
          0x4F5F,   0xD9A1,
          0x4F60,   0xC4E3,
          0x4F63,   0xD3B6,
          0x4F64,   0xD8F4,
          0x4F65,   0xD9DD,
          0x4F67,   0xD8FB,
          0x4F69,   0xC5E5,
          0x4F6C,   0xC0D0,
          0x4F6F,   0xD1F0,
          0x4F70,   0xB0DB,
          0x4F73,   0xBCD1,
          0x4F74,   0xD9A6,
          0x4F76,   0xD9A5,
          0x4F7B,   0xD9AC,
          0x4F7C,   0xD9AE,
          0x4F7E,   0xD9AB,
          0x4F7F,   0xCAB9,
          0x4F83,   0xD9A9,
          0x4F84,   0xD6B6,
          0x4F88,   0xB3DE,
          0x4F89,   0xD9A8,
          0x4F8B,   0xC0FD,
          0x4F8D,   0xCACC,
          0x4F8F,   0xD9AA,
          0x4F91,   0xD9A7,
          0x4F94,   0xD9B0,
          0x4F97,   0xB6B1,
          0x4F9B,   0xB9A9,
          0x4F9D,   0xD2C0,
          0x4FA0,   0xCFC0,
          0x4FA3,   0xC2C2,
          0x4FA5,   0xBDC4,
          0x4FA6,   0xD5EC,
          0x4FA7,   0xB2E0,
          0x4FA8,   0xC7C8,
          0x4FA9,   0xBFEB,
          0x4FAA,   0xD9AD,
          0x4FAC,   0xD9AF,
          0x4FAE,   0xCEEA,
          0x4FAF,   0xBAEE,
          0x4FB5,   0xC7D6,
          0x4FBF,   0xB1E3,
          0x4FC3,   0xB4D9,
          0x4FC4,   0xB6ED,
          0x4FC5,   0xD9B4,
          0x4FCA,   0xBFA1,
          0x4FCE,   0xD9DE,
          0x4FCF,   0xC7CE,
          0x4FD0,   0xC0FE,
          0x4FD1,   0xD9B8,
          0x4FD7,   0xCBD7,
          0x4FD8,   0xB7FD,
          0x4FDA,   0xD9B5,
          0x4FDC,   0xD9B7,
          0x4FDD,   0xB1A3,
          0x4FDE,   0xD3E1,
          0x4FDF,   0xD9B9,
          0x4FE1,   0xD0C5,
          0x4FE3,   0xD9B6,
          0x4FE6,   0xD9B1,
          0x4FE8,   0xD9B2,
          0x4FE9,   0xC1A9,
          0x4FEA,   0xD9B3,
          0x4FED,   0xBCF3,
          0x4FEE,   0xD0DE,
          0x4FEF,   0xB8A9,
          0x4FF1,   0xBEE3,
          0x4FF3,   0xD9BD,
          0x4FF8,   0xD9BA,
          0x4FFA,   0xB0B3,
          0x4FFE,   0xD9C2,
          0x500C,   0xD9C4,
          0x500D,   0xB1B6,
          0x500F,   0xD9BF,
          0x5012,   0xB5B9,
          0x5014,   0xBEF3,
          0x5018,   0xCCC8,
          0x5019,   0xBAF2,
          0x501A,   0xD2D0,
          0x501C,   0xD9C3,
          0x501F,   0xBDE8,
          0x5021,   0xB3AB,
          0x5025,   0xD9C5,
          0x5026,   0xBEEB,
          0x5028,   0xD9C6,
          0x5029,   0xD9BB,
          0x502A,   0xC4DF,
          0x502C,   0xD9BE,
          0x502D,   0xD9C1,
          0x502E,   0xD9C0,
          0x503A,   0xD5AE,
          0x503C,   0xD6B5,
          0x503E,   0xC7E3,
          0x5043,   0xD9C8,
          0x5047,   0xBCD9,
          0x5048,   0xD9CA,
          0x504C,   0xD9BC,
          0x504E,   0xD9CB,
          0x504F,   0xC6AB,
          0x5055,   0xD9C9,
          0x505A,   0xD7F6,
          0x505C,   0xCDA3,
          0x5065,   0xBDA1,
          0x506C,   0xD9CC,
          0x5076,   0xC5BC,
          0x5077,   0xCDB5,
          0x507B,   0xD9CD,
          0x507E,   0xD9C7,
          0x507F,   0xB3A5,
          0x5080,   0xBFFE,
          0x5085,   0xB8B5,
          0x5088,   0xC0FC,
          0x508D,   0xB0F8,
          0x50A3,   0xB4F6,
          0x50A5,   0xD9CE,
          0x50A7,   0xD9CF,
          0x50A8,   0xB4A2,
          0x50A9,   0xD9D0,
          0x50AC,   0xB4DF,
          0x50B2,   0xB0C1,
          0x50BA,   0xD9D1,
          0x50BB,   0xC9B5,
          0x50CF,   0xCFF1,
          0x50D6,   0xD9D2,
          0x50DA,   0xC1C5,
          0x50E6,   0xD9D6,
          0x50E7,   0xC9AE,
          0x50EC,   0xD9D5,
          0x50ED,   0xD9D4,
          0x50EE,   0xD9D7,
          0x50F3,   0xCBDB,
          0x50F5,   0xBDA9,
          0x50FB,   0xC6A7,
          0x5106,   0xD9D3,
          0x5107,   0xD9D8,
          0x510B,   0xD9D9,
          0x5112,   0xC8E5,
          0x5121,   0xC0DC,
          0x513F,   0xB6F9,
          0x5140,   0xD8A3,
          0x5141,   0xD4CA,
          0x5143,   0xD4AA,
          0x5144,   0xD0D6,
          0x5145,   0xB3E4,
          0x5146,   0xD5D7,
          0x5148,   0xCFC8,
          0x5149,   0xB9E2,
          0x514B,   0xBFCB,
          0x514D,   0xC3E2,
          0x5151,   0xB6D2,
          0x5154,   0xCDC3,
          0x5155,   0xD9EE,
          0x5156,   0xD9F0,
          0x515A,   0xB5B3,
          0x515C,   0xB6B5,
          0x5162,   0xBEA4,
          0x5165,   0xC8EB,
          0x5168,   0xC8AB,
          0x516B,   0xB0CB,
          0x516C,   0xB9AB,
          0x516D,   0xC1F9,
          0x516E,   0xD9E2,
          0x5170,   0xC0BC,
          0x5171,   0xB9B2,
          0x5173,   0xB9D8,
          0x5174,   0xD0CB,
          0x5175,   0xB1F8,
          0x5176,   0xC6E4,
          0x5177,   0xBEDF,
          0x5178,   0xB5E4,
          0x5179,   0xD7C8,
          0x517B,   0xD1F8,
          0x517C,   0xBCE6,
          0x517D,   0xCADE,
          0x5180,   0xBCBD,
          0x5181,   0xD9E6,
          0x5182,   0xD8E7,
          0x5185,   0xC4DA,
          0x5188,   0xB8D4,
          0x5189,   0xC8BD,
          0x518C,   0xB2E1,
          0x518D,   0xD4D9,
          0x5192,   0xC3B0,
          0x5195,   0xC3E1,
          0x5196,   0xDAA2,
          0x5197,   0xC8DF,
          0x5199,   0xD0B4,
          0x519B,   0xBEFC,
          0x519C,   0xC5A9,
          0x51A0,   0xB9DA,
          0x51A2,   0xDAA3,
          0x51A4,   0xD4A9,
          0x51A5,   0xDAA4,
          0x51AB,   0xD9FB,
          0x51AC,   0xB6AC,
          0x51AF,   0xB7EB,
          0x51B0,   0xB1F9,
          0x51B1,   0xD9FC,
          0x51B2,   0xB3E5,
          0x51B3,   0xBEF6,
          0x51B5,   0xBFF6,
          0x51B6,   0xD2B1,
          0x51B7,   0xC0E4,
          0x51BB,   0xB6B3,
          0x51BC,   0xD9FE,
          0x51BD,   0xD9FD,
          0x51C0,   0xBEBB,
          0x51C4,   0xC6E0,
          0x51C6,   0xD7BC,
          0x51C7,   0xDAA1,
          0x51C9,   0xC1B9,
          0x51CB,   0xB5F2,
          0x51CC,   0xC1E8,
          0x51CF,   0xBCF5,
          0x51D1,   0xB4D5,
          0x51DB,   0xC1DD,
          0x51DD,   0xC4FD,
          0x51E0,   0xBCB8,
          0x51E1,   0xB7B2,
          0x51E4,   0xB7EF,
          0x51EB,   0xD9EC,
          0x51ED,   0xC6BE,
          0x51EF,   0xBFAD,
          0x51F0,   0xBBCB,
          0x51F3,   0xB5CA,
          0x51F5,   0xDBC9,
          0x51F6,   0xD0D7,
          0x51F8,   0xCDB9,
          0x51F9,   0xB0BC,
          0x51FA,   0xB3F6,
          0x51FB,   0xBBF7,
          0x51FC,   0xDBCA,
          0x51FD,   0xBAAF,
          0x51FF,   0xD4E4,
          0x5200,   0xB5B6,
          0x5201,   0xB5F3,
          0x5202,   0xD8D6,
          0x5203,   0xC8D0,
          0x5206,   0xB7D6,
          0x5207,   0xC7D0,
          0x5208,   0xD8D7,
          0x520A,   0xBFAF,
          0x520D,   0xDBBB,
          0x520E,   0xD8D8,
          0x5211,   0xD0CC,
          0x5212,   0xBBAE,
          0x5216,   0xEBBE,
          0x5217,   0xC1D0,
          0x5218,   0xC1F5,
          0x5219,   0xD4F2,
          0x521A,   0xB8D5,
          0x521B,   0xB4B4,
          0x521D,   0xB3F5,
          0x5220,   0xC9BE,
          0x5224,   0xC5D0,
          0x5228,   0xC5D9,
          0x5229,   0xC0FB,
          0x522B,   0xB1F0,
          0x522D,   0xD8D9,
          0x522E,   0xB9CE,
          0x5230,   0xB5BD,
          0x5233,   0xD8DA,
          0x5236,   0xD6C6,
          0x5237,   0xCBA2,
          0x5238,   0xC8AF,
          0x5239,   0xC9B2,
          0x523A,   0xB4CC,
          0x523B,   0xBFCC,
          0x523D,   0xB9F4,
          0x523F,   0xD8DB,
          0x5240,   0xD8DC,
          0x5241,   0xB6E7,
          0x5242,   0xBCC1,
          0x5243,   0xCCEA,
          0x524A,   0xCFF7,
          0x524C,   0xD8DD,
          0x524D,   0xC7B0,
          0x5250,   0xB9D0,
          0x5251,   0xBDA3,
          0x5254,   0xCCDE,
          0x5256,   0xC6CA,
          0x525C,   0xD8E0,
          0x525E,   0xD8DE,
          0x5261,   0xD8DF,
          0x5265,   0xB0FE,
          0x5267,   0xBEE7,
          0x5269,   0xCAA3,
          0x526A,   0xBCF4,
          0x526F,   0xB8B1,
          0x5272,   0xB8EE,
          0x527D,   0xD8E2,
          0x527F,   0xBDCB,
          0x5281,   0xD8E4,
          0x5282,   0xD8E3,
          0x5288,   0xC5FC,
          0x5290,   0xD8E5,
          0x5293,   0xD8E6,
          0x529B,   0xC1A6,
          0x529D,   0xC8B0,
          0x529E,   0xB0EC,
          0x529F,   0xB9A6,
          0x52A0,   0xBCD3,
          0x52A1,   0xCEF1,
          0x52A2,   0xDBBD,
          0x52A3,   0xC1D3,
          0x52A8,   0xB6AF,
          0x52A9,   0xD6FA,
          0x52AA,   0xC5AC,
          0x52AB,   0xBDD9,
          0x52AC,   0xDBBE,
          0x52AD,   0xDBBF,
          0x52B1,   0xC0F8,
          0x52B2,   0xBEA2,
          0x52B3,   0xC0CD,
          0x52BE,   0xDBC0,
          0x52BF,   0xCAC6,
          0x52C3,   0xB2AA,
          0x52C7,   0xD3C2,
          0x52C9,   0xC3E3,
          0x52CB,   0xD1AB,
          0x52D0,   0xDBC2,
          0x52D2,   0xC0D5,
          0x52D6,   0xDBC3,
          0x52D8,   0xBFB1,
          0x52DF,   0xC4BC,
          0x52E4,   0xC7DA,
          0x52F0,   0xDBC4,
          0x52F9,   0xD9E8,
          0x52FA,   0xC9D7,
          0x52FE,   0xB9B4,
          0x52FF,   0xCEF0,
          0x5300,   0xD4C8,
          0x5305,   0xB0FC,
          0x5306,   0xB4D2,
          0x5308,   0xD0D9,
          0x530D,   0xD9E9,
          0x530F,   0xDECB,
          0x5310,   0xD9EB,
          0x5315,   0xD8B0,
          0x5316,   0xBBAF,
          0x5317,   0xB1B1,
          0x5319,   0xB3D7,
          0x531A,   0xD8CE,
          0x531D,   0xD4D1,
          0x5320,   0xBDB3,
          0x5321,   0xBFEF,
          0x5323,   0xCFBB,
          0x5326,   0xD8D0,
          0x532A,   0xB7CB,
          0x532E,   0xD8D1,
          0x5339,   0xC6A5,
          0x533A,   0xC7F8,
          0x533B,   0xD2BD,
          0x533E,   0xD8D2,
          0x533F,   0xC4E4,
          0x5341,   0xCAAE,
          0x5343,   0xC7A7,
          0x5345,   0xD8A6,
          0x5347,   0xC9FD,
          0x5348,   0xCEE7,
          0x5349,   0xBBDC,
          0x534A,   0xB0EB,
          0x534E,   0xBBAA,
          0x534F,   0xD0AD,
          0x5351,   0xB1B0,
          0x5352,   0xD7E4,
          0x5353,   0xD7BF,
          0x5355,   0xB5A5,
          0x5356,   0xC2F4,
          0x5357,   0xC4CF,
          0x535A,   0xB2A9,
          0x535C,   0xB2B7,
          0x535E,   0xB1E5,
          0x535F,   0xDFB2,
          0x5360,   0xD5BC,
          0x5361,   0xBFA8,
          0x5362,   0xC2AC,
          0x5363,   0xD8D5,
          0x5364,   0xC2B1,
          0x5366,   0xD8D4,
          0x5367,   0xCED4,
          0x5369,   0xDAE0,
          0x536B,   0xCEC0,
          0x536E,   0xD8B4,
          0x536F,   0xC3AE,
          0x5370,   0xD3A1,
          0x5371,   0xCEA3,
          0x5373,   0xBCB4,
          0x5374,   0xC8B4,
          0x5375,   0xC2D1,
          0x5377,   0xBEED,
          0x5378,   0xD0B6,
          0x537A,   0xDAE1,
          0x537F,   0xC7E4,
          0x5382,   0xB3A7,
          0x5384,   0xB6F2,
          0x5385,   0xCCFC,
          0x5386,   0xC0FA,
          0x5389,   0xC0F7,
          0x538B,   0xD1B9,
          0x538C,   0xD1E1,
          0x538D,   0xD8C7,
          0x5395,   0xB2DE,
          0x5398,   0xC0E5,
          0x539A,   0xBAF1,
          0x539D,   0xD8C8,
          0x539F,   0xD4AD,
          0x53A2,   0xCFE1,
          0x53A3,   0xD8C9,
          0x53A5,   0xD8CA,
          0x53A6,   0xCFC3,
          0x53A8,   0xB3F8,
          0x53A9,   0xBEC7,
          0x53AE,   0xD8CB,
          0x53B6,   0xDBCC,
          0x53BB,   0xC8A5,
          0x53BF,   0xCFD8,
          0x53C1,   0xC8FE,
          0x53C2,   0xB2CE,
          0x53C8,   0xD3D6,
          0x53C9,   0xB2E6,
          0x53CA,   0xBCB0,
          0x53CB,   0xD3D1,
          0x53CC,   0xCBAB,
          0x53CD,   0xB7B4,
          0x53D1,   0xB7A2,
          0x53D4,   0xCAE5,
          0x53D6,   0xC8A1,
          0x53D7,   0xCADC,
          0x53D8,   0xB1E4,
          0x53D9,   0xD0F0,
          0x53DB,   0xC5D1,
          0x53DF,   0xDBC5,
          0x53E0,   0xB5FE,
          0x53E3,   0xBFDA,
          0x53E4,   0xB9C5,
          0x53E5,   0xBEE4,
          0x53E6,   0xC1ED,
          0x53E8,   0xDFB6,
          0x53E9,   0xDFB5,
          0x53EA,   0xD6BB,
          0x53EB,   0xBDD0,
          0x53EC,   0xD5D9,
          0x53ED,   0xB0C8,
          0x53EE,   0xB6A3,
          0x53EF,   0xBFC9,
          0x53F0,   0xCCA8,
          0x53F1,   0xDFB3,
          0x53F2,   0xCAB7,
          0x53F3,   0xD3D2,
          0x53F5,   0xD8CF,
          0x53F6,   0xD2B6,
          0x53F7,   0xBAC5,
          0x53F8,   0xCBBE,
          0x53F9,   0xCCBE,
          0x53FB,   0xDFB7,
          0x53FC,   0xB5F0,
          0x53FD,   0xDFB4,
          0x5401,   0xD3F5,
          0x5403,   0xB3D4,
          0x5404,   0xB8F7,
          0x5406,   0xDFBA,
          0x5408,   0xBACF,
          0x5409,   0xBCAA,
          0x540A,   0xB5F5,
          0x540C,   0xCDAC,
          0x540D,   0xC3FB,
          0x540E,   0xBAF3,
          0x540F,   0xC0F4,
          0x5410,   0xCDC2,
          0x5411,   0xCFF2,
          0x5412,   0xDFB8,
          0x5413,   0xCFC5,
          0x5415,   0xC2C0,
          0x5416,   0xDFB9,
          0x5417,   0xC2F0,
          0x541B,   0xBEFD,
          0x541D,   0xC1DF,
          0x541E,   0xCDCC,
          0x541F,   0xD2F7,
          0x5420,   0xB7CD,
          0x5421,   0xDFC1,
          0x5423,   0xDFC4,
          0x5426,   0xB7F1,
          0x5427,   0xB0C9,
          0x5428,   0xB6D6,
          0x5429,   0xB7D4,
          0x542B,   0xBAAC,
          0x542C,   0xCCFD,
          0x542D,   0xBFD4,
          0x542E,   0xCBB1,
          0x542F,   0xC6F4,
          0x5431,   0xD6A8,
          0x5432,   0xDFC5,
          0x5434,   0xCEE2,
          0x5435,   0xB3B3,
          0x5438,   0xCEFC,
          0x5439,   0xB4B5,
          0x543B,   0xCEC7,
          0x543C,   0xBAF0,
          0x543E,   0xCEE1,
          0x5440,   0xD1BD,
          0x5443,   0xDFC0,
          0x5446,   0xB4F4,
          0x5448,   0xB3CA,
          0x544A,   0xB8E6,
          0x544B,   0xDFBB,
          0x5450,   0xC4C5,
          0x5452,   0xDFBC,
          0x5453,   0xDFBD,
          0x5454,   0xDFBE,
          0x5455,   0xC5BB,
          0x5456,   0xDFBF,
          0x5457,   0xDFC2,
          0x5458,   0xD4B1,
          0x5459,   0xDFC3,
          0x545B,   0xC7BA,
          0x545C,   0xCED8,
          0x5462,   0xC4D8,
          0x5464,   0xDFCA,
          0x5466,   0xDFCF,
          0x5468,   0xD6DC,
          0x5471,   0xDFC9,
          0x5472,   0xDFDA,
          0x5473,   0xCEB6,
          0x5475,   0xBAC7,
          0x5476,   0xDFCE,
          0x5477,   0xDFC8,
          0x5478,   0xC5DE,
          0x547B,   0xC9EB,
          0x547C,   0xBAF4,
          0x547D,   0xC3FC,
          0x5480,   0xBED7,
          0x5482,   0xDFC6,
          0x5484,   0xDFCD,
          0x5486,   0xC5D8,
          0x548B,   0xD5A6,
          0x548C,   0xBACD,
          0x548E,   0xBECC,
          0x548F,   0xD3BD,
          0x5490,   0xB8C0,
          0x5492,   0xD6E4,
          0x5494,   0xDFC7,
          0x5495,   0xB9BE,
          0x5496,   0xBFA7,
          0x5499,   0xC1FC,
          0x549A,   0xDFCB,
          0x549B,   0xDFCC,
          0x549D,   0xDFD0,
          0x54A3,   0xDFDB,
          0x54A4,   0xDFE5,
          0x54A6,   0xDFD7,
          0x54A7,   0xDFD6,
          0x54A8,   0xD7C9,
          0x54A9,   0xDFE3,
          0x54AA,   0xDFE4,
          0x54AB,   0xE5EB,
          0x54AC,   0xD2A7,
          0x54AD,   0xDFD2,
          0x54AF,   0xBFA9,
          0x54B1,   0xD4DB,
          0x54B3,   0xBFC8,
          0x54B4,   0xDFD4,
          0x54B8,   0xCFCC,
          0x54BB,   0xDFDD,
          0x54BD,   0xD1CA,
          0x54BF,   0xDFDE,
          0x54C0,   0xB0A7,
          0x54C1,   0xC6B7,
          0x54C2,   0xDFD3,
          0x54C4,   0xBAE5,
          0x54C6,   0xB6DF,
          0x54C7,   0xCDDB,
          0x54C8,   0xB9FE,
          0x54C9,   0xD4D5,
          0x54CC,   0xDFDF,
          0x54CD,   0xCFEC,
          0x54CE,   0xB0A5,
          0x54CF,   0xDFE7,
          0x54D0,   0xDFD1,
          0x54D1,   0xD1C6,
          0x54D2,   0xDFD5,
          0x54D3,   0xDFD8,
          0x54D4,   0xDFD9,
          0x54D5,   0xDFDC,
          0x54D7,   0xBBA9,
          0x54D9,   0xDFE0,
          0x54DA,   0xDFE1,
          0x54DC,   0xDFE2,
          0x54DD,   0xDFE6,
          0x54DE,   0xDFE8,
          0x54DF,   0xD3B4,
          0x54E5,   0xB8E7,
          0x54E6,   0xC5B6,
          0x54E7,   0xDFEA,
          0x54E8,   0xC9DA,
          0x54E9,   0xC1A8,
          0x54EA,   0xC4C4,
          0x54ED,   0xBFDE,
          0x54EE,   0xCFF8,
          0x54F2,   0xD5DC,
          0x54F3,   0xDFEE,
          0x54FA,   0xB2B8,
          0x54FC,   0xBADF,
          0x54FD,   0xDFEC,
          0x54FF,   0xDBC1,
          0x5501,   0xD1E4,
          0x5506,   0xCBF4,
          0x5507,   0xB4BD,
          0x5509,   0xB0A6,
          0x550F,   0xDFF1,
          0x5510,   0xCCC6,
          0x5511,   0xDFF2,
          0x5514,   0xDFED,
          0x551B,   0xDFE9,
          0x5520,   0xDFEB,
          0x5522,   0xDFEF,
          0x5523,   0xDFF0,
          0x5524,   0xBBBD,
          0x5527,   0xDFF3,
          0x552A,   0xDFF4,
          0x552C,   0xBBA3,
          0x552E,   0xCADB,
          0x552F,   0xCEA8,
          0x5530,   0xE0A7,
          0x5531,   0xB3AA,
          0x5533,   0xE0A6,
          0x5537,   0xE0A1,
          0x553C,   0xDFFE,
          0x553E,   0xCDD9,
          0x553F,   0xDFFC,
          0x5541,   0xDFFA,
          0x5543,   0xBFD0,
          0x5544,   0xD7C4,
          0x5546,   0xC9CC,
          0x5549,   0xDFF8,
          0x554A,   0xB0A1,
          0x5550,   0xDFFD,
          0x5555,   0xDFFB,
          0x5556,   0xE0A2,
          0x555C,   0xE0A8,
          0x5561,   0xB7C8,
          0x5564,   0xC6A1,
          0x5565,   0xC9B6,
          0x5566,   0xC0B2,
          0x5567,   0xDFF5,
          0x556A,   0xC5BE,
          0x556C,   0xD8C4,
          0x556D,   0xDFF9,
          0x556E,   0xC4F6,
          0x5575,   0xE0A3,
          0x5576,   0xE0A4,
          0x5577,   0xE0A5,
          0x5578,   0xD0A5,
          0x557B,   0xE0B4,
          0x557C,   0xCCE4,
          0x557E,   0xE0B1,
          0x5580,   0xBFA6,
          0x5581,   0xE0AF,
          0x5582,   0xCEB9,
          0x5583,   0xE0AB,
          0x5584,   0xC9C6,
          0x5587,   0xC0AE,
          0x5588,   0xE0AE,
          0x5589,   0xBAED,
          0x558A,   0xBAB0,
          0x558B,   0xE0A9,
          0x558F,   0xDFF6,
          0x5591,   0xE0B3,
          0x5594,   0xE0B8,
          0x5598,   0xB4AD,
          0x5599,   0xE0B9,
          0x559C,   0xCFB2,
          0x559D,   0xBAC8,
          0x559F,   0xE0B0,
          0x55A7,   0xD0FA,
          0x55B1,   0xE0AC,
          0x55B3,   0xD4FB,
          0x55B5,   0xDFF7,
          0x55B7,   0xC5E7,
          0x55B9,   0xE0AD,
          0x55BB,   0xD3F7,
          0x55BD,   0xE0B6,
          0x55BE,   0xE0B7,
          0x55C4,   0xE0C4,
          0x55C5,   0xD0E1,
          0x55C9,   0xE0BC,
          0x55CC,   0xE0C9,
          0x55CD,   0xE0CA,
          0x55D1,   0xE0BE,
          0x55D2,   0xE0AA,
          0x55D3,   0xC9A4,
          0x55D4,   0xE0C1,
          0x55D6,   0xE0B2,
          0x55DC,   0xCAC8,
          0x55DD,   0xE0C3,
          0x55DF,   0xE0B5,
          0x55E1,   0xCECB,
          0x55E3,   0xCBC3,
          0x55E4,   0xE0CD,
          0x55E5,   0xE0C6,
          0x55E6,   0xE0C2,
          0x55E8,   0xE0CB,
          0x55EA,   0xE0BA,
          0x55EB,   0xE0BF,
          0x55EC,   0xE0C0,
          0x55EF,   0xE0C5,
          0x55F2,   0xE0C7,
          0x55F3,   0xE0C8,
          0x55F5,   0xE0CC,
          0x55F7,   0xE0BB,
          0x55FD,   0xCBD4,
          0x55FE,   0xE0D5,
          0x5600,   0xE0D6,
          0x5601,   0xE0D2,
          0x5608,   0xE0D0,
          0x5609,   0xBCCE,
          0x560C,   0xE0D1,
          0x560E,   0xB8C2,
          0x560F,   0xD8C5,
          0x5618,   0xD0EA,
          0x561B,   0xC2EF,
          0x561E,   0xE0CF,
          0x561F,   0xE0BD,
          0x5623,   0xE0D4,
          0x5624,   0xE0D3,
          0x5627,   0xE0D7,
          0x562C,   0xE0DC,
          0x562D,   0xE0D8,
          0x5631,   0xD6F6,
          0x5632,   0xB3B0,
          0x5634,   0xD7EC,
          0x5636,   0xCBBB,
          0x5639,   0xE0DA,
          0x563B,   0xCEFB,
          0x563F,   0xBAD9,
          0x564C,   0xE0E1,
          0x564D,   0xE0DD,
          0x564E,   0xD2AD,
          0x5654,   0xE0E2,
          0x5657,   0xE0DB,
          0x5658,   0xE0D9,
          0x5659,   0xE0DF,
          0x565C,   0xE0E0,
          0x5662,   0xE0DE,
          0x5664,   0xE0E4,
          0x5668,   0xC6F7,
          0x5669,   0xD8AC,
          0x566A,   0xD4EB,
          0x566B,   0xE0E6,
          0x566C,   0xCAC9,
          0x5671,   0xE0E5,
          0x5676,   0xB8C1,
          0x567B,   0xE0E7,
          0x567C,   0xE0E8,
          0x5685,   0xE0E9,
          0x5686,   0xE0E3,
          0x568E,   0xBABF,
          0x568F,   0xCCE7,
          0x5693,   0xE0EA,
          0x56A3,   0xCFF9,
          0x56AF,   0xE0EB,
          0x56B7,   0xC8C2,
          0x56BC,   0xBDC0,
          0x56CA,   0xC4D2,
          0x56D4,   0xE0EC,
          0x56D7,   0xE0ED,
          0x56DA,   0xC7F4,
          0x56DB,   0xCBC4,
          0x56DD,   0xE0EE,
          0x56DE,   0xBBD8,
          0x56DF,   0xD8B6,
          0x56E0,   0xD2F2,
          0x56E1,   0xE0EF,
          0x56E2,   0xCDC5,
          0x56E4,   0xB6DA,
          0x56EB,   0xE0F1,
          0x56ED,   0xD4B0,
          0x56F0,   0xC0A7,
          0x56F1,   0xB4D1,
          0x56F4,   0xCEA7,
          0x56F5,   0xE0F0,
          0x56F9,   0xE0F2,
          0x56FA,   0xB9CC,
          0x56FD,   0xB9FA,
          0x56FE,   0xCDBC,
          0x56FF,   0xE0F3,
          0x5703,   0xC6D4,
          0x5704,   0xE0F4,
          0x5706,   0xD4B2,
          0x5708,   0xC8A6,
          0x5709,   0xE0F6,
          0x570A,   0xE0F5,
          0x571C,   0xE0F7,
          0x571F,   0xCDC1,
          0x5723,   0xCAA5,
          0x5728,   0xD4DA,
          0x5729,   0xDBD7,
          0x572A,   0xDBD9,
          0x572C,   0xDBD8,
          0x572D,   0xB9E7,
          0x572E,   0xDBDC,
          0x572F,   0xDBDD,
          0x5730,   0xB5D8,
          0x5733,   0xDBDA,
          0x5739,   0xDBDB,
          0x573A,   0xB3A1,
          0x573B,   0xDBDF,
          0x573E,   0xBBF8,
          0x5740,   0xD6B7,
          0x5742,   0xDBE0,
          0x5747,   0xBEF9,
          0x574A,   0xB7BB,
          0x574C,   0xDBD0,
          0x574D,   0xCCAE,
          0x574E,   0xBFB2,
          0x574F,   0xBBB5,
          0x5750,   0xD7F8,
          0x5751,   0xBFD3,
          0x5757,   0xBFE9,
          0x575A,   0xBCE1,
          0x575B,   0xCCB3,
          0x575C,   0xDBDE,
          0x575D,   0xB0D3,
          0x575E,   0xCEEB,
          0x575F,   0xB7D8,
          0x5760,   0xD7B9,
          0x5761,   0xC6C2,
          0x5764,   0xC0A4,
          0x5766,   0xCCB9,
          0x5768,   0xDBE7,
          0x5769,   0xDBE1,
          0x576A,   0xC6BA,
          0x576B,   0xDBE3,
          0x576D,   0xDBE8,
          0x576F,   0xC5F7,
          0x5773,   0xDBEA,
          0x5776,   0xDBE9,
          0x5777,   0xBFC0,
          0x577B,   0xDBE6,
          0x577C,   0xDBE5,
          0x5782,   0xB4B9,
          0x5783,   0xC0AC,
          0x5784,   0xC2A2,
          0x5785,   0xDBE2,
          0x5786,   0xDBE4,
          0x578B,   0xD0CD,
          0x578C,   0xDBED,
          0x5792,   0xC0DD,
          0x5793,   0xDBF2,
          0x579B,   0xB6E2,
          0x57A0,   0xDBF3,
          0x57A1,   0xDBD2,
          0x57A2,   0xB9B8,
          0x57A3,   0xD4AB,
          0x57A4,   0xDBEC,
          0x57A6,   0xBFD1,
          0x57A7,   0xDBF0,
          0x57A9,   0xDBD1,
          0x57AB,   0xB5E6,
          0x57AD,   0xDBEB,
          0x57AE,   0xBFE5,
          0x57B2,   0xDBEE,
          0x57B4,   0xDBF1,
          0x57B8,   0xDBF9,
          0x57C2,   0xB9A1,
          0x57C3,   0xB0A3,
          0x57CB,   0xC2F1,
          0x57CE,   0xB3C7,
          0x57CF,   0xDBEF,
          0x57D2,   0xDBF8,
          0x57D4,   0xC6D2,
          0x57D5,   0xDBF4,
          0x57D8,   0xDBF5,
          0x57D9,   0xDBF7,
          0x57DA,   0xDBF6,
          0x57DD,   0xDBFE,
          0x57DF,   0xD3F2,
          0x57E0,   0xB2BA,
          0x57E4,   0xDBFD,
          0x57ED,   0xDCA4,
          0x57EF,   0xDBFB,
          0x57F4,   0xDBFA,
          0x57F8,   0xDBFC,
          0x57F9,   0xC5E0,
          0x57FA,   0xBBF9,
          0x57FD,   0xDCA3,
          0x5800,   0xDCA5,
          0x5802,   0xCCC3,
          0x5806,   0xB6D1,
          0x5807,   0xDDC0,
          0x580B,   0xDCA1,
          0x580D,   0xDCA2,
          0x5811,   0xC7B5,
          0x5815,   0xB6E9,
          0x5819,   0xDCA7,
          0x581E,   0xDCA6,
          0x5820,   0xDCA9,
          0x5821,   0xB1A4,
          0x5824,   0xB5CC,
          0x582A,   0xBFB0,
          0x5830,   0xD1DF,
          0x5835,   0xB6C2,
          0x5844,   0xDCA8,
          0x584C,   0xCBFA,
          0x584D,   0xEBF3,
          0x5851,   0xCBDC,
          0x5854,   0xCBFE,
          0x5858,   0xCCC1,
          0x585E,   0xC8FB,
          0x5865,   0xDCAA,
          0x586B,   0xCCEE,
          0x586C,   0xDCAB,
          0x587E,   0xDBD3,
          0x5880,   0xDCAF,
          0x5881,   0xDCAC,
          0x5883,   0xBEB3,
          0x5885,   0xCAFB,
          0x5889,   0xDCAD,
          0x5892,   0xC9CA,
          0x5893,   0xC4B9,
          0x5899,   0xC7BD,
          0x589A,   0xDCAE,
          0x589E,   0xD4F6,
          0x589F,   0xD0E6,
          0x58A8,   0xC4AB,
          0x58A9,   0xB6D5,
          0x58BC,   0xDBD4,
          0x58C1,   0xB1DA,
          0x58C5,   0xDBD5,
          0x58D1,   0xDBD6,
          0x58D5,   0xBABE,
          0x58E4,   0xC8C0,
          0x58EB,   0xCABF,
          0x58EC,   0xC8C9,
          0x58EE,   0xD7B3,
          0x58F0,   0xC9F9,
          0x58F3,   0xBFC7,
          0x58F6,   0xBAF8,
          0x58F9,   0xD2BC,
          0x5902,   0xE2BA,
          0x5904,   0xB4A6,
          0x5907,   0xB1B8,
          0x590D,   0xB8B4,
          0x590F,   0xCFC4,
          0x5914,   0xD9E7,
          0x5915,   0xCFA6,
          0x5916,   0xCDE2,
          0x5919,   0xD9ED,
          0x591A,   0xB6E0,
          0x591C,   0xD2B9,
          0x591F,   0xB9BB,
          0x5924,   0xE2B9,
          0x5925,   0xE2B7,
          0x5927,   0xB4F3,
          0x5929,   0xCCEC,
          0x592A,   0xCCAB,
          0x592B,   0xB7F2,
          0x592D,   0xD8B2,
          0x592E,   0xD1EB,
          0x592F,   0xBABB,
          0x5931,   0xCAA7,
          0x5934,   0xCDB7,
          0x5937,   0xD2C4,
          0x5938,   0xBFE4,
          0x5939,   0xBCD0,
          0x593A,   0xB6E1,
          0x593C,   0xDEC5,
          0x5941,   0xDEC6,
          0x5942,   0xDBBC,
          0x5944,   0xD1D9,
          0x5947,   0xC6E6,
          0x5948,   0xC4CE,
          0x5949,   0xB7EE,
          0x594B,   0xB7DC,
          0x594E,   0xBFFC,
          0x594F,   0xD7E0,
          0x5951,   0xC6F5,
          0x5954,   0xB1BC,
          0x5955,   0xDEC8,
          0x5956,   0xBDB1,
          0x5957,   0xCCD7,
          0x5958,   0xDECA,
          0x595A,   0xDEC9,
          0x5960,   0xB5EC,
          0x5962,   0xC9DD,
          0x5965,   0xB0C2,
          0x5973,   0xC5AE,
          0x5974,   0xC5AB,
          0x5976,   0xC4CC,
          0x5978,   0xBCE9,
          0x5979,   0xCBFD,
          0x597D,   0xBAC3,
          0x5981,   0xE5F9,
          0x5982,   0xC8E7,
          0x5983,   0xE5FA,
          0x5984,   0xCDFD,
          0x5986,   0xD7B1,
          0x5987,   0xB8BE,
          0x5988,   0xC2E8,
          0x598A,   0xC8D1,
          0x598D,   0xE5FB,
          0x5992,   0xB6CA,
          0x5993,   0xBCCB,
          0x5996,   0xD1FD,
          0x5997,   0xE6A1,
          0x5999,   0xC3EE,
          0x599E,   0xE6A4,
          0x59A3,   0xE5FE,
          0x59A4,   0xE6A5,
          0x59A5,   0xCDD7,
          0x59A8,   0xB7C1,
          0x59A9,   0xE5FC,
          0x59AA,   0xE5FD,
          0x59AB,   0xE6A3,
          0x59AE,   0xC4DD,
          0x59AF,   0xE6A8,
          0x59B2,   0xE6A7,
          0x59B9,   0xC3C3,
          0x59BB,   0xC6DE,
          0x59BE,   0xE6AA,
          0x59C6,   0xC4B7,
          0x59CA,   0xE6A2,
          0x59CB,   0xCABC,
          0x59D0,   0xBDE3,
          0x59D1,   0xB9C3,
          0x59D2,   0xE6A6,
          0x59D3,   0xD0D5,
          0x59D4,   0xCEAF,
          0x59D7,   0xE6A9,
          0x59D8,   0xE6B0,
          0x59DA,   0xD2A6,
          0x59DC,   0xBDAA,
          0x59DD,   0xE6AD,
          0x59E3,   0xE6AF,
          0x59E5,   0xC0D1,
          0x59E8,   0xD2CC,
          0x59EC,   0xBCA7,
          0x59F9,   0xE6B1,
          0x59FB,   0xD2F6,
          0x59FF,   0xD7CB,
          0x5A01,   0xCDFE,
          0x5A03,   0xCDDE,
          0x5A04,   0xC2A6,
          0x5A05,   0xE6AB,
          0x5A06,   0xE6AC,
          0x5A07,   0xBDBF,
          0x5A08,   0xE6AE,
          0x5A09,   0xE6B3,
          0x5A0C,   0xE6B2,
          0x5A11,   0xE6B6,
          0x5A13,   0xE6B8,
          0x5A18,   0xC4EF,
          0x5A1C,   0xC4C8,
          0x5A1F,   0xBEEA,
          0x5A20,   0xC9EF,
          0x5A23,   0xE6B7,
          0x5A25,   0xB6F0,
          0x5A29,   0xC3E4,
          0x5A31,   0xD3E9,
          0x5A32,   0xE6B4,
          0x5A34,   0xE6B5,
          0x5A36,   0xC8A2,
          0x5A3C,   0xE6BD,
          0x5A40,   0xE6B9,
          0x5A46,   0xC6C5,
          0x5A49,   0xCDF1,
          0x5A4A,   0xE6BB,
          0x5A55,   0xE6BC,
          0x5A5A,   0xBBE9,
          0x5A62,   0xE6BE,
          0x5A67,   0xE6BA,
          0x5A6A,   0xC0B7,
          0x5A74,   0xD3A4,
          0x5A75,   0xE6BF,
          0x5A76,   0xC9F4,
          0x5A77,   0xE6C3,
          0x5A7A,   0xE6C4,
          0x5A7F,   0xD0F6,
          0x5A92,   0xC3BD,
          0x5A9A,   0xC3C4,
          0x5A9B,   0xE6C2,
          0x5AAA,   0xE6C1,
          0x5AB2,   0xE6C7,
          0x5AB3,   0xCFB1,
          0x5AB5,   0xEBF4,
          0x5AB8,   0xE6CA,
          0x5ABE,   0xE6C5,
          0x5AC1,   0xBCDE,
          0x5AC2,   0xC9A9,
          0x5AC9,   0xBCB5,
          0x5ACC,   0xCFD3,
          0x5AD2,   0xE6C8,
          0x5AD4,   0xE6C9,
          0x5AD6,   0xE6CE,
          0x5AD8,   0xE6D0,
          0x5ADC,   0xE6D1,
          0x5AE0,   0xE6CB,
          0x5AE1,   0xB5D5,
          0x5AE3,   0xE6CC,
          0x5AE6,   0xE6CF,
          0x5AE9,   0xC4DB,
          0x5AEB,   0xE6C6,
          0x5AF1,   0xE6CD,
          0x5B09,   0xE6D2,
          0x5B16,   0xE6D4,
          0x5B17,   0xE6D3,
          0x5B32,   0xE6D5,
          0x5B34,   0xD9F8,
          0x5B37,   0xE6D6,
          0x5B40,   0xE6D7,
          0x5B50,   0xD7D3,
          0x5B51,   0xE6DD,
          0x5B53,   0xE6DE,
          0x5B54,   0xBFD7,
          0x5B55,   0xD4D0,
          0x5B57,   0xD7D6,
          0x5B58,   0xB4E6,
          0x5B59,   0xCBEF,
          0x5B5A,   0xE6DA,
          0x5B5B,   0xD8C3,
          0x5B5C,   0xD7CE,
          0x5B5D,   0xD0A2,
          0x5B5F,   0xC3CF,
          0x5B62,   0xE6DF,
          0x5B63,   0xBCBE,
          0x5B64,   0xB9C2,
          0x5B65,   0xE6DB,
          0x5B66,   0xD1A7,
          0x5B69,   0xBAA2,
          0x5B6A,   0xC2CF,
          0x5B6C,   0xD8AB,
          0x5B70,   0xCAEB,
          0x5B71,   0xE5EE,
          0x5B73,   0xE6DC,
          0x5B75,   0xB7F5,
          0x5B7A,   0xC8E6,
          0x5B7D,   0xC4F5,
          0x5B80,   0xE5B2,
          0x5B81,   0xC4FE,
          0x5B83,   0xCBFC,
          0x5B84,   0xE5B3,
          0x5B85,   0xD5AC,
          0x5B87,   0xD3EE,
          0x5B88,   0xCAD8,
          0x5B89,   0xB0B2,
          0x5B8B,   0xCBCE,
          0x5B8C,   0xCDEA,
          0x5B8F,   0xBAEA,
          0x5B93,   0xE5B5,
          0x5B95,   0xE5B4,
          0x5B97,   0xD7DA,
          0x5B98,   0xB9D9,
          0x5B99,   0xD6E6,
          0x5B9A,   0xB6A8,
          0x5B9B,   0xCDF0,
          0x5B9C,   0xD2CB,
          0x5B9D,   0xB1A6,
          0x5B9E,   0xCAB5,
          0x5BA0,   0xB3E8,
          0x5BA1,   0xC9F3,
          0x5BA2,   0xBFCD,
          0x5BA3,   0xD0FB,
          0x5BA4,   0xCAD2,
          0x5BA5,   0xE5B6,
          0x5BA6,   0xBBC2,
          0x5BAA,   0xCFDC,
          0x5BAB,   0xB9AC,
          0x5BB0,   0xD4D7,
          0x5BB3,   0xBAA6,
          0x5BB4,   0xD1E7,
          0x5BB5,   0xCFFC,
          0x5BB6,   0xBCD2,
          0x5BB8,   0xE5B7,
          0x5BB9,   0xC8DD,
          0x5BBD,   0xBFED,
          0x5BBE,   0xB1F6,
          0x5BBF,   0xCBDE,
          0x5BC2,   0xBCC5,
          0x5BC4,   0xBCC4,
          0x5BC5,   0xD2FA,
          0x5BC6,   0xC3DC,
          0x5BC7,   0xBFDC,
          0x5BCC,   0xB8BB,
          0x5BD0,   0xC3C2,
          0x5BD2,   0xBAAE,
          0x5BD3,   0xD4A2,
          0x5BDD,   0xC7DE,
          0x5BDE,   0xC4AF,
          0x5BDF,   0xB2EC,
          0x5BE1,   0xB9D1,
          0x5BE4,   0xE5BB,
          0x5BE5,   0xC1C8,
          0x5BE8,   0xD5AF,
          0x5BEE,   0xE5BC,
          0x5BF0,   0xE5BE,
          0x5BF8,   0xB4E7,
          0x5BF9,   0xB6D4,
          0x5BFA,   0xCBC2,
          0x5BFB,   0xD1B0,
          0x5BFC,   0xB5BC,
          0x5BFF,   0xCAD9,
          0x5C01,   0xB7E2,
          0x5C04,   0xC9E4,
          0x5C06,   0xBDAB,
          0x5C09,   0xCEBE,
          0x5C0A,   0xD7F0,
          0x5C0F,   0xD0A1,
          0x5C11,   0xC9D9,
          0x5C14,   0xB6FB,
          0x5C15,   0xE6D8,
          0x5C16,   0xBCE2,
          0x5C18,   0xB3BE,
          0x5C1A,   0xC9D0,
          0x5C1C,   0xE6D9,
          0x5C1D,   0xB3A2,
          0x5C22,   0xDECC,
          0x5C24,   0xD3C8,
          0x5C25,   0xDECD,
          0x5C27,   0xD2A2,
          0x5C2C,   0xDECE,
          0x5C31,   0xBECD,
          0x5C34,   0xDECF,
          0x5C38,   0xCAAC,
          0x5C39,   0xD2FC,
          0x5C3A,   0xB3DF,
          0x5C3B,   0xE5EA,
          0x5C3C,   0xC4E1,
          0x5C3D,   0xBEA1,
          0x5C3E,   0xCEB2,
          0x5C3F,   0xC4F2,
          0x5C40,   0xBED6,
          0x5C41,   0xC6A8,
          0x5C42,   0xB2E3,
          0x5C45,   0xBED3,
          0x5C48,   0xC7FC,
          0x5C49,   0xCCEB,
          0x5C4A,   0xBDEC,
          0x5C4B,   0xCEDD,
          0x5C4E,   0xCABA,
          0x5C4F,   0xC6C1,
          0x5C50,   0xE5EC,
          0x5C51,   0xD0BC,
          0x5C55,   0xD5B9,
          0x5C59,   0xE5ED,
          0x5C5E,   0xCAF4,
          0x5C60,   0xCDC0,
          0x5C61,   0xC2C5,
          0x5C63,   0xE5EF,
          0x5C65,   0xC2C4,
          0x5C66,   0xE5F0,
          0x5C6E,   0xE5F8,
          0x5C6F,   0xCDCD,
          0x5C71,   0xC9BD,
          0x5C79,   0xD2D9,
          0x5C7A,   0xE1A8,
          0x5C7F,   0xD3EC,
          0x5C81,   0xCBEA,
          0x5C82,   0xC6F1,
          0x5C88,   0xE1AC,
          0x5C8C,   0xE1A7,
          0x5C8D,   0xE1A9,
          0x5C90,   0xE1AA,
          0x5C91,   0xE1AF,
          0x5C94,   0xB2ED,
          0x5C96,   0xE1AB,
          0x5C97,   0xB8DA,
          0x5C98,   0xE1AD,
          0x5C99,   0xE1AE,
          0x5C9A,   0xE1B0,
          0x5C9B,   0xB5BA,
          0x5C9C,   0xE1B1,
          0x5CA2,   0xE1B3,
          0x5CA3,   0xE1B8,
          0x5CA9,   0xD1D2,
          0x5CAB,   0xE1B6,
          0x5CAC,   0xE1B5,
          0x5CAD,   0xC1EB,
          0x5CB1,   0xE1B7,
          0x5CB3,   0xD4C0,
          0x5CB5,   0xE1B2,
          0x5CB7,   0xE1BA,
          0x5CB8,   0xB0B6,
          0x5CBD,   0xE1B4,
          0x5CBF,   0xBFF9,
          0x5CC1,   0xE1B9,
          0x5CC4,   0xE1BB,
          0x5CCB,   0xE1BE,
          0x5CD2,   0xE1BC,
          0x5CD9,   0xD6C5,
          0x5CE1,   0xCFBF,
          0x5CE4,   0xE1BD,
          0x5CE5,   0xE1BF,
          0x5CE6,   0xC2CD,
          0x5CE8,   0xB6EB,
          0x5CEA,   0xD3F8,
          0x5CED,   0xC7CD,
          0x5CF0,   0xB7E5,
          0x5CFB,   0xBEFE,
          0x5D02,   0xE1C0,
          0x5D03,   0xE1C1,
          0x5D06,   0xE1C7,
          0x5D07,   0xB3E7,
          0x5D0E,   0xC6E9,
          0x5D14,   0xB4DE,
          0x5D16,   0xD1C2,
          0x5D1B,   0xE1C8,
          0x5D1E,   0xE1C6,
          0x5D24,   0xE1C5,
          0x5D26,   0xE1C3,
          0x5D27,   0xE1C2,
          0x5D29,   0xB1C0,
          0x5D2D,   0xD5B8,
          0x5D2E,   0xE1C4,
          0x5D34,   0xE1CB,
          0x5D3D,   0xE1CC,
          0x5D3E,   0xE1CA,
          0x5D47,   0xEFFA,
          0x5D4A,   0xE1D3,
          0x5D4B,   0xE1D2,
          0x5D4C,   0xC7B6,
          0x5D58,   0xE1C9,
          0x5D5B,   0xE1CE,
          0x5D5D,   0xE1D0,
          0x5D69,   0xE1D4,
          0x5D6B,   0xE1D1,
          0x5D6C,   0xE1CD,
          0x5D6F,   0xE1CF,
          0x5D74,   0xE1D5,
          0x5D82,   0xE1D6,
          0x5D99,   0xE1D7,
          0x5D9D,   0xE1D8,
          0x5DB7,   0xE1DA,
          0x5DC5,   0xE1DB,
          0x5DCD,   0xCEA1,
          0x5DDB,   0xE7DD,
          0x5DDD,   0xB4A8,
          0x5DDE,   0xD6DD,
          0x5DE1,   0xD1B2,
          0x5DE2,   0xB3B2,
          0x5DE5,   0xB9A4,
          0x5DE6,   0xD7F3,
          0x5DE7,   0xC7C9,
          0x5DE8,   0xBEDE,
          0x5DE9,   0xB9AE,
          0x5DEB,   0xCED7,
          0x5DEE,   0xB2EE,
          0x5DEF,   0xDBCF,
          0x5DF1,   0xBCBA,
          0x5DF2,   0xD2D1,
          0x5DF3,   0xCBC8,
          0x5DF4,   0xB0CD,
          0x5DF7,   0xCFEF,
          0x5DFD,   0xD9E3,
          0x5DFE,   0xBDED,
          0x5E01,   0xB1D2,
          0x5E02,   0xCAD0,
          0x5E03,   0xB2BC,
          0x5E05,   0xCBA7,
          0x5E06,   0xB7AB,
          0x5E08,   0xCAA6,
          0x5E0C,   0xCFA3,
          0x5E0F,   0xE0F8,
          0x5E10,   0xD5CA,
          0x5E11,   0xE0FB,
          0x5E14,   0xE0FA,
          0x5E15,   0xC5C1,
          0x5E16,   0xCCFB,
          0x5E18,   0xC1B1,
          0x5E19,   0xE0F9,
          0x5E1A,   0xD6E3,
          0x5E1B,   0xB2AF,
          0x5E1C,   0xD6C4,
          0x5E1D,   0xB5DB,
          0x5E26,   0xB4F8,
          0x5E27,   0xD6A1,
          0x5E2D,   0xCFAF,
          0x5E2E,   0xB0EF,
          0x5E31,   0xE0FC,
          0x5E37,   0xE1A1,
          0x5E38,   0xB3A3,
          0x5E3B,   0xE0FD,
          0x5E3C,   0xE0FE,
          0x5E3D,   0xC3B1,
          0x5E42,   0xC3DD,
          0x5E44,   0xE1A2,
          0x5E45,   0xB7F9,
          0x5E4C,   0xBBCF,
          0x5E54,   0xE1A3,
          0x5E55,   0xC4BB,
          0x5E5B,   0xE1A4,
          0x5E5E,   0xE1A5,
          0x5E61,   0xE1A6,
          0x5E62,   0xB4B1,
          0x5E72,   0xB8C9,
          0x5E73,   0xC6BD,
          0x5E74,   0xC4EA,
          0x5E76,   0xB2A2,
          0x5E78,   0xD0D2,
          0x5E7A,   0xE7DB,
          0x5E7B,   0xBBC3,
          0x5E7C,   0xD3D7,
          0x5E7D,   0xD3C4,
          0x5E7F,   0xB9E3,
          0x5E80,   0xE2CF,
          0x5E84,   0xD7AF,
          0x5E86,   0xC7EC,
          0x5E87,   0xB1D3,
          0x5E8A,   0xB4B2,
          0x5E8B,   0xE2D1,
          0x5E8F,   0xD0F2,
          0x5E90,   0xC2AE,
          0x5E91,   0xE2D0,
          0x5E93,   0xBFE2,
          0x5E94,   0xD3A6,
          0x5E95,   0xB5D7,
          0x5E96,   0xE2D2,
          0x5E97,   0xB5EA,
          0x5E99,   0xC3ED,
          0x5E9A,   0xB8FD,
          0x5E9C,   0xB8AE,
          0x5E9E,   0xC5D3,
          0x5E9F,   0xB7CF,
          0x5EA0,   0xE2D4,
          0x5EA5,   0xE2D3,
          0x5EA6,   0xB6C8,
          0x5EA7,   0xD7F9,
          0x5EAD,   0xCDA5,
          0x5EB3,   0xE2D8,
          0x5EB5,   0xE2D6,
          0x5EB6,   0xCAFC,
          0x5EB7,   0xBFB5,
          0x5EB8,   0xD3B9,
          0x5EB9,   0xE2D5,
          0x5EBE,   0xE2D7,
          0x5EC9,   0xC1AE,
          0x5ECA,   0xC0C8,
          0x5ED1,   0xE2DB,
          0x5ED2,   0xE2DA,
          0x5ED3,   0xC0AA,
          0x5ED6,   0xC1CE,
          0x5EDB,   0xE2DC,
          0x5EE8,   0xE2DD,
          0x5EEA,   0xE2DE,
          0x5EF4,   0xDBC8,
          0x5EF6,   0xD1D3,
          0x5EF7,   0xCDA2,
          0x5EFA,   0xBDA8,
          0x5EFE,   0xDEC3,
          0x5EFF,   0xD8A5,
          0x5F00,   0xBFAA,
          0x5F01,   0xDBCD,
          0x5F02,   0xD2EC,
          0x5F03,   0xC6FA,
          0x5F04,   0xC5AA,
          0x5F08,   0xDEC4,
          0x5F0A,   0xB1D7,
          0x5F0B,   0xDFAE,
          0x5F0F,   0xCABD,
          0x5F11,   0xDFB1,
          0x5F13,   0xB9AD,
          0x5F15,   0xD2FD,
          0x5F17,   0xB8A5,
          0x5F18,   0xBAEB,
          0x5F1B,   0xB3DA,
          0x5F1F,   0xB5DC,
          0x5F20,   0xD5C5,
          0x5F25,   0xC3D6,
          0x5F26,   0xCFD2,
          0x5F27,   0xBBA1,
          0x5F29,   0xE5F3,
          0x5F2A,   0xE5F2,
          0x5F2D,   0xE5F4,
          0x5F2F,   0xCDE4,
          0x5F31,   0xC8F5,
          0x5F39,   0xB5AF,
          0x5F3A,   0xC7BF,
          0x5F3C,   0xE5F6,
          0x5F40,   0xECB0,
          0x5F50,   0xE5E6,
          0x5F52,   0xB9E9,
          0x5F53,   0xB5B1,
          0x5F55,   0xC2BC,
          0x5F56,   0xE5E8,
          0x5F57,   0xE5E7,
          0x5F58,   0xE5E9,
          0x5F5D,   0xD2CD,
          0x5F61,   0xE1EA,
          0x5F62,   0xD0CE,
          0x5F64,   0xCDAE,
          0x5F66,   0xD1E5,
          0x5F69,   0xB2CA,
          0x5F6A,   0xB1EB,
          0x5F6C,   0xB1F2,
          0x5F6D,   0xC5ED,
          0x5F70,   0xD5C3,
          0x5F71,   0xD3B0,
          0x5F73,   0xE1DC,
          0x5F77,   0xE1DD,
          0x5F79,   0xD2DB,
          0x5F7B,   0xB3B9,
          0x5F7C,   0xB1CB,
          0x5F80,   0xCDF9,
          0x5F81,   0xD5F7,
          0x5F82,   0xE1DE,
          0x5F84,   0xBEB6,
          0x5F85,   0xB4FD,
          0x5F87,   0xE1DF,
          0x5F88,   0xBADC,
          0x5F89,   0xE1E0,
          0x5F8A,   0xBBB2,
          0x5F8B,   0xC2C9,
          0x5F8C,   0xE1E1,
          0x5F90,   0xD0EC,
          0x5F92,   0xCDBD,
          0x5F95,   0xE1E2,
          0x5F97,   0xB5C3,
          0x5F98,   0xC5C7,
          0x5F99,   0xE1E3,
          0x5F9C,   0xE1E4,
          0x5FA1,   0xD3F9,
          0x5FA8,   0xE1E5,
          0x5FAA,   0xD1AD,
          0x5FAD,   0xE1E6,
          0x5FAE,   0xCEA2,
          0x5FB5,   0xE1E7,
          0x5FB7,   0xB5C2,
          0x5FBC,   0xE1E8,
          0x5FBD,   0xBBD5,
          0x5FC3,   0xD0C4,
          0x5FC4,   0xE2E0,
          0x5FC5,   0xB1D8,
          0x5FC6,   0xD2E4,
          0x5FC9,   0xE2E1,
          0x5FCC,   0xBCC9,
          0x5FCD,   0xC8CC,
          0x5FCF,   0xE2E3,
          0x5FD0,   0xECFE,
          0x5FD1,   0xECFD,
          0x5FD2,   0xDFAF,
          0x5FD6,   0xE2E2,
          0x5FD7,   0xD6BE,
          0x5FD8,   0xCDFC,
          0x5FD9,   0xC3A6,
          0x5FDD,   0xE3C3,
          0x5FE0,   0xD6D2,
          0x5FE1,   0xE2E7,
          0x5FE4,   0xE2E8,
          0x5FE7,   0xD3C7,
          0x5FEA,   0xE2EC,
          0x5FEB,   0xBFEC,
          0x5FED,   0xE2ED,
          0x5FEE,   0xE2E5,
          0x5FF1,   0xB3C0,
          0x5FF5,   0xC4EE,
          0x5FF8,   0xE2EE,
          0x5FFB,   0xD0C3,
          0x5FFD,   0xBAF6,
          0x5FFE,   0xE2E9,
          0x5FFF,   0xB7DE,
          0x6000,   0xBBB3,
          0x6001,   0xCCAC,
          0x6002,   0xCBCB,
          0x6003,   0xE2E4,
          0x6004,   0xE2E6,
          0x6005,   0xE2EA,
          0x6006,   0xE2EB,
          0x600A,   0xE2F7,
          0x600D,   0xE2F4,
          0x600E,   0xD4F5,
          0x600F,   0xE2F3,
          0x6012,   0xC5AD,
          0x6014,   0xD5FA,
          0x6015,   0xC5C2,
          0x6016,   0xB2C0,
          0x6019,   0xE2EF,
          0x601B,   0xE2F2,
          0x601C,   0xC1AF,
          0x601D,   0xCBBC,
          0x6020,   0xB5A1,
          0x6021,   0xE2F9,
          0x6025,   0xBCB1,
          0x6026,   0xE2F1,
          0x6027,   0xD0D4,
          0x6028,   0xD4B9,
          0x6029,   0xE2F5,
          0x602A,   0xB9D6,
          0x602B,   0xE2F6,
          0x602F,   0xC7D3,
          0x6035,   0xE2F0,
          0x603B,   0xD7DC,
          0x603C,   0xEDA1,
          0x603F,   0xE2F8,
          0x6041,   0xEDA5,
          0x6042,   0xE2FE,
          0x6043,   0xCAD1,
          0x604B,   0xC1B5,
          0x604D,   0xBBD0,
          0x6050,   0xBFD6,
          0x6052,   0xBAE3,
          0x6055,   0xCBA1,
          0x6059,   0xEDA6,
          0x605A,   0xEDA3,
          0x605D,   0xEDA2,
          0x6062,   0xBBD6,
          0x6063,   0xEDA7,
          0x6064,   0xD0F4,
          0x6067,   0xEDA4,
          0x6068,   0xBADE,
          0x6069,   0xB6F7,
          0x606A,   0xE3A1,
          0x606B,   0xB6B2,
          0x606C,   0xCCF1,
          0x606D,   0xB9A7,
          0x606F,   0xCFA2,
          0x6070,   0xC7A1,
          0x6073,   0xBFD2,
          0x6076,   0xB6F1,
          0x6078,   0xE2FA,
          0x6079,   0xE2FB,
          0x607A,   0xE2FD,
          0x607B,   0xE2FC,
          0x607C,   0xC4D5,
          0x607D,   0xE3A2,
          0x607F,   0xD3C1,
          0x6083,   0xE3A7,
          0x6084,   0xC7C4,
          0x6089,   0xCFA4,
          0x608C,   0xE3A9,
          0x608D,   0xBAB7,
          0x6092,   0xE3A8,
          0x6094,   0xBBDA,
          0x6096,   0xE3A3,
          0x609A,   0xE3A4,
          0x609B,   0xE3AA,
          0x609D,   0xE3A6,
          0x609F,   0xCEF2,
          0x60A0,   0xD3C6,
          0x60A3,   0xBBBC,
          0x60A6,   0xD4C3,
          0x60A8,   0xC4FA,
          0x60AB,   0xEDA8,
          0x60AC,   0xD0FC,
          0x60AD,   0xE3A5,
          0x60AF,   0xC3F5,
          0x60B1,   0xE3AD,
          0x60B2,   0xB1AF,
          0x60B4,   0xE3B2,
          0x60B8,   0xBCC2,
          0x60BB,   0xE3AC,
          0x60BC,   0xB5BF,
          0x60C5,   0xC7E9,
          0x60C6,   0xE3B0,
          0x60CA,   0xBEAA,
          0x60CB,   0xCDEF,
          0x60D1,   0xBBF3,
          0x60D5,   0xCCE8,
          0x60D8,   0xE3AF,
          0x60DA,   0xE3B1,
          0x60DC,   0xCFA7,
          0x60DD,   0xE3AE,
          0x60DF,   0xCEA9,
          0x60E0,   0xBBDD,
          0x60E6,   0xB5EB,
          0x60E7,   0xBEE5,
          0x60E8,   0xB2D2,
          0x60E9,   0xB3CD,
          0x60EB,   0xB1B9,
          0x60EC,   0xE3AB,
          0x60ED,   0xB2D1,
          0x60EE,   0xB5AC,
          0x60EF,   0xB9DF,
          0x60F0,   0xB6E8,
          0x60F3,   0xCFEB,
          0x60F4,   0xE3B7,
          0x60F6,   0xBBCC,
          0x60F9,   0xC8C7,
          0x60FA,   0xD0CA,
          0x6100,   0xE3B8,
          0x6101,   0xB3EE,
          0x6106,   0xEDA9,
          0x6108,   0xD3FA,
          0x6109,   0xD3E4,
          0x610D,   0xEDAA,
          0x610E,   0xE3B9,
          0x610F,   0xD2E2,
          0x6115,   0xE3B5,
          0x611A,   0xD3DE,
          0x611F,   0xB8D0,
          0x6120,   0xE3B3,
          0x6123,   0xE3B6,
          0x6124,   0xB7DF,
          0x6126,   0xE3B4,
          0x6127,   0xC0A2,
          0x612B,   0xE3BA,
          0x613F,   0xD4B8,
          0x6148,   0xB4C8,
          0x614A,   0xE3BB,
          0x614C,   0xBBC5,
          0x614E,   0xC9F7,
          0x6151,   0xC9E5,
          0x6155,   0xC4BD,
          0x615D,   0xEDAB,
          0x6162,   0xC2FD,
          0x6167,   0xBBDB,
          0x6168,   0xBFAE,
          0x6170,   0xCEBF,
          0x6175,   0xE3BC,
          0x6177,   0xBFB6,
          0x618B,   0xB1EF,
          0x618E,   0xD4F7,
          0x6194,   0xE3BE,
          0x619D,   0xEDAD,
          0x61A7,   0xE3BF,
          0x61A8,   0xBAA9,
          0x61A9,   0xEDAC,
          0x61AC,   0xE3BD,
          0x61B7,   0xE3C0,
          0x61BE,   0xBAB6,
          0x61C2,   0xB6AE,
          0x61C8,   0xD0B8,
          0x61CA,   0xB0C3,
          0x61CB,   0xEDAE,
          0x61D1,   0xEDAF,
          0x61D2,   0xC0C1,
          0x61D4,   0xE3C1,
          0x61E6,   0xC5B3,
          0x61F5,   0xE3C2,
          0x61FF,   0xDCB2,
          0x6206,   0xEDB0,
          0x6208,   0xB8EA,
          0x620A,   0xCEEC,
          0x620B,   0xEAA7,
          0x620C,   0xD0E7,
          0x620D,   0xCAF9,
          0x620E,   0xC8D6,
          0x620F,   0xCFB7,
          0x6210,   0xB3C9,
          0x6211,   0xCED2,
          0x6212,   0xBDE4,
          0x6215,   0xE3DE,
          0x6216,   0xBBF2,
          0x6217,   0xEAA8,
          0x6218,   0xD5BD,
          0x621A,   0xC6DD,
          0x621B,   0xEAA9,
          0x621F,   0xEAAA,
          0x6221,   0xEAAC,
          0x6222,   0xEAAB,
          0x6224,   0xEAAE,
          0x6225,   0xEAAD,
          0x622A,   0xBDD8,
          0x622C,   0xEAAF,
          0x622E,   0xC2BE,
          0x6233,   0xB4C1,
          0x6234,   0xB4F7,
          0x6237,   0xBBA7,
          0x623D,   0xECE6,
          0x623E,   0xECE5,
          0x623F,   0xB7BF,
          0x6240,   0xCBF9,
          0x6241,   0xB1E2,
          0x6243,   0xECE7,
          0x6247,   0xC9C8,
          0x6248,   0xECE8,
          0x6249,   0xECE9,
          0x624B,   0xCAD6,
          0x624C,   0xDED0,
          0x624D,   0xB2C5,
          0x624E,   0xD4FA,
          0x6251,   0xC6CB,
          0x6252,   0xB0C7,
          0x6253,   0xB4F2,
          0x6254,   0xC8D3,
          0x6258,   0xCDD0,
          0x625B,   0xBFB8,
          0x6263,   0xBFDB,
          0x6266,   0xC7A4,
          0x6267,   0xD6B4,
          0x6269,   0xC0A9,
          0x626A,   0xDED1,
          0x626B,   0xC9A8,
          0x626C,   0xD1EF,
          0x626D,   0xC5A4,
          0x626E,   0xB0E7,
          0x626F,   0xB3B6,
          0x6270,   0xC8C5,
          0x6273,   0xB0E2,
          0x6276,   0xB7F6,
          0x6279,   0xC5FA,
          0x627C,   0xB6F3,
          0x627E,   0xD5D2,
          0x627F,   0xB3D0,
          0x6280,   0xBCBC,
          0x6284,   0xB3AD,
          0x6289,   0xBEF1,
          0x628A,   0xB0D1,
          0x6291,   0xD2D6,
          0x6292,   0xCAE3,
          0x6293,   0xD7A5,
          0x6295,   0xCDB6,
          0x6296,   0xB6B6,
          0x6297,   0xBFB9,
          0x6298,   0xD5DB,
          0x629A,   0xB8A7,
          0x629B,   0xC5D7,
          0x629F,   0xDED2,
          0x62A0,   0xBFD9,
          0x62A1,   0xC2D5,
          0x62A2,   0xC7C0,
          0x62A4,   0xBBA4,
          0x62A5,   0xB1A8,
          0x62A8,   0xC5EA,
          0x62AB,   0xC5FB,
          0x62AC,   0xCCA7,
          0x62B1,   0xB1A7,
          0x62B5,   0xB5D6,
          0x62B9,   0xC4A8,
          0x62BB,   0xDED3,
          0x62BC,   0xD1BA,
          0x62BD,   0xB3E9,
          0x62BF,   0xC3F2,
          0x62C2,   0xB7F7,
          0x62C4,   0xD6F4,
          0x62C5,   0xB5A3,
          0x62C6,   0xB2F0,
          0x62C7,   0xC4B4,
          0x62C8,   0xC4E9,
          0x62C9,   0xC0AD,
          0x62CA,   0xDED4,
          0x62CC,   0xB0E8,
          0x62CD,   0xC5C4,
          0x62CE,   0xC1E0,
          0x62D0,   0xB9D5,
          0x62D2,   0xBEDC,
          0x62D3,   0xCDD8,
          0x62D4,   0xB0CE,
          0x62D6,   0xCDCF,
          0x62D7,   0xDED6,
          0x62D8,   0xBED0,
          0x62D9,   0xD7BE,
          0x62DA,   0xDED5,
          0x62DB,   0xD5D0,
          0x62DC,   0xB0DD,
          0x62DF,   0xC4E2,
          0x62E2,   0xC2A3,
          0x62E3,   0xBCF0,
          0x62E5,   0xD3B5,
          0x62E6,   0xC0B9,
          0x62E7,   0xC5A1,
          0x62E8,   0xB2A6,
          0x62E9,   0xD4F1,
          0x62EC,   0xC0A8,
          0x62ED,   0xCAC3,
          0x62EE,   0xDED7,
          0x62EF,   0xD5FC,
          0x62F1,   0xB9B0,
          0x62F3,   0xC8AD,
          0x62F4,   0xCBA9,
          0x62F6,   0xDED9,
          0x62F7,   0xBFBD,
          0x62FC,   0xC6B4,
          0x62FD,   0xD7A7,
          0x62FE,   0xCAB0,
          0x62FF,   0xC4C3,
          0x6301,   0xB3D6,
          0x6302,   0xB9D2,
          0x6307,   0xD6B8,
          0x6308,   0xEAFC,
          0x6309,   0xB0B4,
          0x630E,   0xBFE6,
          0x6311,   0xCCF4,
          0x6316,   0xCDDA,
          0x631A,   0xD6BF,
          0x631B,   0xC2CE,
          0x631D,   0xCECE,
          0x631E,   0xCCA2,
          0x631F,   0xD0AE,
          0x6320,   0xC4D3,
          0x6321,   0xB5B2,
          0x6322,   0xDED8,
          0x6323,   0xD5F5,
          0x6324,   0xBCB7,
          0x6325,   0xBBD3,
          0x6328,   0xB0A4,
          0x632A,   0xC5B2,
          0x632B,   0xB4EC,
          0x632F,   0xD5F1,
          0x6332,   0xEAFD,
          0x6339,   0xDEDA,
          0x633A,   0xCDA6,
          0x633D,   0xCDEC,
          0x6342,   0xCEE6,
          0x6343,   0xDEDC,
          0x6345,   0xCDB1,
          0x6346,   0xC0A6,
          0x6349,   0xD7BD,
          0x634B,   0xDEDB,
          0x634C,   0xB0C6,
          0x634D,   0xBAB4,
          0x634E,   0xC9D3,
          0x634F,   0xC4F3,
          0x6350,   0xBEE8,
          0x6355,   0xB2B6,
          0x635E,   0xC0CC,
          0x635F,   0xCBF0,
          0x6361,   0xBCF1,
          0x6362,   0xBBBB,
          0x6363,   0xB5B7,
          0x6367,   0xC5F5,
          0x6369,   0xDEE6,
          0x636D,   0xDEE3,
          0x636E,   0xBEDD,
          0x6371,   0xDEDF,
          0x6376,   0xB4B7,
          0x6377,   0xBDDD,
          0x637A,   0xDEE0,
          0x637B,   0xC4ED,
          0x6380,   0xCFC6,
          0x6382,   0xB5E0,
          0x6387,   0xB6DE,
          0x6388,   0xCADA,
          0x6389,   0xB5F4,
          0x638A,   0xDEE5,
          0x638C,   0xD5C6,
          0x638E,   0xDEE1,
          0x638F,   0xCCCD,
          0x6390,   0xC6FE,
          0x6392,   0xC5C5,
          0x6396,   0xD2B4,
          0x6398,   0xBEF2,
          0x63A0,   0xC2D3,
          0x63A2,   0xCCBD,
          0x63A3,   0xB3B8,
          0x63A5,   0xBDD3,
          0x63A7,   0xBFD8,
          0x63A8,   0xCDC6,
          0x63A9,   0xD1DA,
          0x63AA,   0xB4EB,
          0x63AC,   0xDEE4,
          0x63AD,   0xDEDD,
          0x63AE,   0xDEE7,
          0x63B0,   0xEAFE,
          0x63B3,   0xC2B0,
          0x63B4,   0xDEE2,
          0x63B7,   0xD6C0,
          0x63B8,   0xB5A7,
          0x63BA,   0xB2F4,
          0x63BC,   0xDEE8,
          0x63BE,   0xDEF2,
          0x63C4,   0xDEED,
          0x63C6,   0xDEF1,
          0x63C9,   0xC8E0,
          0x63CD,   0xD7E1,
          0x63CE,   0xDEEF,
          0x63CF,   0xC3E8,
          0x63D0,   0xCCE1,
          0x63D2,   0xB2E5,
          0x63D6,   0xD2BE,
          0x63DE,   0xDEEE,
          0x63E0,   0xDEEB,
          0x63E1,   0xCED5,
          0x63E3,   0xB4A7,
          0x63E9,   0xBFAB,
          0x63EA,   0xBEBE,
          0x63ED,   0xBDD2,
          0x63F2,   0xDEE9,
          0x63F4,   0xD4AE,
          0x63F6,   0xDEDE,
          0x63F8,   0xDEEA,
          0x63FD,   0xC0BF,
          0x63FF,   0xDEEC,
          0x6400,   0xB2F3,
          0x6401,   0xB8E9,
          0x6402,   0xC2A7,
          0x6405,   0xBDC1,
          0x640B,   0xDEF5,
          0x640C,   0xDEF8,
          0x640F,   0xB2AB,
          0x6410,   0xB4A4,
          0x6413,   0xB4EA,
          0x6414,   0xC9A6,
          0x641B,   0xDEF6,
          0x641C,   0xCBD1,
          0x641E,   0xB8E3,
          0x6420,   0xDEF7,
          0x6421,   0xDEFA,
          0x6426,   0xDEF9,
          0x642A,   0xCCC2,
          0x642C,   0xB0E1,
          0x642D,   0xB4EE,
          0x6434,   0xE5BA,
          0x643A,   0xD0AF,
          0x643D,   0xB2EB,
          0x643F,   0xEBA1,
          0x6441,   0xDEF4,
          0x6444,   0xC9E3,
          0x6445,   0xDEF3,
          0x6446,   0xB0DA,
          0x6447,   0xD2A1,
          0x6448,   0xB1F7,
          0x644A,   0xCCAF,
          0x6452,   0xDEF0,
          0x6454,   0xCBA4,
          0x6458,   0xD5AA,
          0x645E,   0xDEFB,
          0x6467,   0xB4DD,
          0x6469,   0xC4A6,
          0x646D,   0xDEFD,
          0x6478,   0xC3FE,
          0x6479,   0xC4A1,
          0x647A,   0xDFA1,
          0x6482,   0xC1CC,
          0x6484,   0xDEFC,
          0x6485,   0xBEEF,
          0x6487,   0xC6B2,
          0x6491,   0xB3C5,
          0x6492,   0xC8F6,
          0x6495,   0xCBBA,
          0x6496,   0xDEFE,
          0x6499,   0xDFA4,
          0x649E,   0xD7B2,
          0x64A4,   0xB3B7,
          0x64A9,   0xC1C3,
          0x64AC,   0xC7CB,
          0x64AD,   0xB2A5,
          0x64AE,   0xB4E9,
          0x64B0,   0xD7AB,
          0x64B5,   0xC4EC,
          0x64B7,   0xDFA2,
          0x64B8,   0xDFA3,
          0x64BA,   0xDFA5,
          0x64BC,   0xBAB3,
          0x64C0,   0xDFA6,
          0x64C2,   0xC0DE,
          0x64C5,   0xC9C3,
          0x64CD,   0xB2D9,
          0x64CE,   0xC7E6,
          0x64D0,   0xDFA7,
          0x64D2,   0xC7DC,
          0x64D7,   0xDFA8,
          0x64D8,   0xEBA2,
          0x64DE,   0xCBD3,
          0x64E2,   0xDFAA,
          0x64E4,   0xDFA9,
          0x64E6,   0xB2C1,
          0x6500,   0xC5CA,
          0x6509,   0xDFAB,
          0x6512,   0xD4DC,
          0x6518,   0xC8C1,
          0x6525,   0xDFAC,
          0x652B,   0xBEF0,
          0x652E,   0xDFAD,
          0x652F,   0xD6A7,
          0x6534,   0xEAB7,
          0x6535,   0xEBB6,
          0x6536,   0xCAD5,
          0x6538,   0xD8FC,
          0x6539,   0xB8C4,
          0x653B,   0xB9A5,
          0x653E,   0xB7C5,
          0x653F,   0xD5FE,
          0x6545,   0xB9CA,
          0x6548,   0xD0A7,
          0x6549,   0xF4CD,
          0x654C,   0xB5D0,
          0x654F,   0xC3F4,
          0x6551,   0xBEC8,
          0x6555,   0xEBB7,
          0x6556,   0xB0BD,
          0x6559,   0xBDCC,
          0x655B,   0xC1B2,
          0x655D,   0xB1D6,
          0x655E,   0xB3A8,
          0x6562,   0xB8D2,
          0x6563,   0xC9A2,
          0x6566,   0xB6D8,
          0x656B,   0xEBB8,
          0x656C,   0xBEB4,
          0x6570,   0xCAFD,
          0x6572,   0xC7C3,
          0x6574,   0xD5FB,
          0x6577,   0xB7F3,
          0x6587,   0xCEC4,
          0x658B,   0xD5AB,
          0x658C,   0xB1F3,
          0x6590,   0xECB3,
          0x6591,   0xB0DF,
          0x6593,   0xECB5,
          0x6597,   0xB6B7,
          0x6599,   0xC1CF,
          0x659B,   0xF5FA,
          0x659C,   0xD0B1,
          0x659F,   0xD5E5,
          0x65A1,   0xCED3,
          0x65A4,   0xBDEF,
          0x65A5,   0xB3E2,
          0x65A7,   0xB8AB,
          0x65A9,   0xD5B6,
          0x65AB,   0xEDBD,
          0x65AD,   0xB6CF,
          0x65AF,   0xCBB9,
          0x65B0,   0xD0C2,
          0x65B9,   0xB7BD,
          0x65BC,   0xECB6,
          0x65BD,   0xCAA9,
          0x65C1,   0xC5D4,
          0x65C3,   0xECB9,
          0x65C4,   0xECB8,
          0x65C5,   0xC2C3,
          0x65C6,   0xECB7,
          0x65CB,   0xD0FD,
          0x65CC,   0xECBA,
          0x65CE,   0xECBB,
          0x65CF,   0xD7E5,
          0x65D2,   0xECBC,
          0x65D6,   0xECBD,
          0x65D7,   0xC6EC,
          0x65E0,   0xCEDE,
          0x65E2,   0xBCC8,
          0x65E5,   0xC8D5,
          0x65E6,   0xB5A9,
          0x65E7,   0xBEC9,
          0x65E8,   0xD6BC,
          0x65E9,   0xD4E7,
          0x65EC,   0xD1AE,
          0x65ED,   0xD0F1,
          0x65EE,   0xEAB8,
          0x65EF,   0xEAB9,
          0x65F0,   0xEABA,
          0x65F1,   0xBAB5,
          0x65F6,   0xCAB1,
          0x65F7,   0xBFF5,
          0x65FA,   0xCDFA,
          0x6600,   0xEAC0,
          0x6602,   0xB0BA,
          0x6603,   0xEABE,
          0x6606,   0xC0A5,
          0x660A,   0xEABB,
          0x660C,   0xB2FD,
          0x660E,   0xC3F7,
          0x660F,   0xBBE8,
          0x6613,   0xD2D7,
          0x6614,   0xCEF4,
          0x6615,   0xEABF,
          0x6619,   0xEABC,
          0x661D,   0xEAC3,
          0x661F,   0xD0C7,
          0x6620,   0xD3B3,
          0x6625,   0xB4BA,
          0x6627,   0xC3C1,
          0x6628,   0xD7F2,
          0x662D,   0xD5D1,
          0x662F,   0xCAC7,
          0x6631,   0xEAC5,
          0x6634,   0xEAC4,
          0x6635,   0xEAC7,
          0x6636,   0xEAC6,
          0x663C,   0xD6E7,
          0x663E,   0xCFD4,
          0x6641,   0xEACB,
          0x6643,   0xBBCE,
          0x664B,   0xBDFA,
          0x664C,   0xC9CE,
          0x664F,   0xEACC,
          0x6652,   0xC9B9,
          0x6653,   0xCFFE,
          0x6654,   0xEACA,
          0x6655,   0xD4CE,
          0x6656,   0xEACD,
          0x6657,   0xEACF,
          0x665A,   0xCDED,
          0x665F,   0xEAC9,
          0x6661,   0xEACE,
          0x6664,   0xCEEE,
          0x6666,   0xBBDE,
          0x6668,   0xB3BF,
          0x666E,   0xC6D5,
          0x666F,   0xBEB0,
          0x6670,   0xCEFA,
          0x6674,   0xC7E7,
          0x6676,   0xBEA7,
          0x6677,   0xEAD0,
          0x667A,   0xD6C7,
          0x667E,   0xC1C0,
          0x6682,   0xD4DD,
          0x6684,   0xEAD1,
          0x6687,   0xCFBE,
          0x668C,   0xEAD2,
          0x6691,   0xCAEE,
          0x6696,   0xC5AF,
          0x6697,   0xB0B5,
          0x669D,   0xEAD4,
          0x66A7,   0xEAD3,
          0x66A8,   0xF4DF,
          0x66AE,   0xC4BA,
          0x66B4,   0xB1A9,
          0x66B9,   0xE5DF,
          0x66BE,   0xEAD5,
          0x66D9,   0xCAEF,
          0x66DB,   0xEAD6,
          0x66DC,   0xEAD7,
          0x66DD,   0xC6D8,
          0x66E6,   0xEAD8,
          0x66E9,   0xEAD9,
          0x66F0,   0xD4BB,
          0x66F2,   0xC7FA,
          0x66F3,   0xD2B7,
          0x66F4,   0xB8FC,
          0x66F7,   0xEAC2,
          0x66F9,   0xB2DC,
          0x66FC,   0xC2FC,
          0x66FE,   0xD4F8,
          0x66FF,   0xCCE6,
          0x6700,   0xD7EE,
          0x6708,   0xD4C2,
          0x6709,   0xD3D0,
          0x670A,   0xEBC3,
          0x670B,   0xC5F3,
          0x670D,   0xB7FE,
          0x6710,   0xEBD4,
          0x6714,   0xCBB7,
          0x6715,   0xEBDE,
          0x6717,   0xC0CA,
          0x671B,   0xCDFB,
          0x671D,   0xB3AF,
          0x671F,   0xC6DA,
          0x6726,   0xEBFC,
          0x6728,   0xC4BE,
          0x672A,   0xCEB4,
          0x672B,   0xC4A9,
          0x672C,   0xB1BE,
          0x672D,   0xD4FD,
          0x672F,   0xCAF5,
          0x6731,   0xD6EC,
          0x6734,   0xC6D3,
          0x6735,   0xB6E4,
          0x673A,   0xBBFA,
          0x673D,   0xD0E0,
          0x6740,   0xC9B1,
          0x6742,   0xD4D3,
          0x6743,   0xC8A8,
          0x6746,   0xB8CB,
          0x6748,   0xE8BE,
          0x6749,   0xC9BC,
          0x674C,   0xE8BB,
          0x674E,   0xC0EE,
          0x674F,   0xD0D3,
          0x6750,   0xB2C4,
          0x6751,   0xB4E5,
          0x6753,   0xE8BC,
          0x6756,   0xD5C8,
          0x675C,   0xB6C5,
          0x675E,   0xE8BD,
          0x675F,   0xCAF8,
          0x6760,   0xB8DC,
          0x6761,   0xCCF5,
          0x6765,   0xC0B4,
          0x6768,   0xD1EE,
          0x6769,   0xE8BF,
          0x676A,   0xE8C2,
          0x676D,   0xBABC,
          0x676F,   0xB1AD,
          0x6770,   0xBDDC,
          0x6772,   0xEABD,
          0x6773,   0xE8C3,
          0x6775,   0xE8C6,
          0x6777,   0xE8CB,
          0x677C,   0xE8CC,
          0x677E,   0xCBC9,
          0x677F,   0xB0E5,
          0x6781,   0xBCAB,
          0x6784,   0xB9B9,
          0x6787,   0xE8C1,
          0x6789,   0xCDF7,
          0x678B,   0xE8CA,
          0x6790,   0xCEF6,
          0x6795,   0xD5ED,
          0x6797,   0xC1D6,
          0x6798,   0xE8C4,
          0x679A,   0xC3B6,
          0x679C,   0xB9FB,
          0x679D,   0xD6A6,
          0x679E,   0xE8C8,
          0x67A2,   0xCAE0,
          0x67A3,   0xD4E6,
          0x67A5,   0xE8C0,
          0x67A7,   0xE8C5,
          0x67A8,   0xE8C7,
          0x67AA,   0xC7B9,
          0x67AB,   0xB7E3,
          0x67AD,   0xE8C9,
          0x67AF,   0xBFDD,
          0x67B0,   0xE8D2,
          0x67B3,   0xE8D7,
          0x67B5,   0xE8D5,
          0x67B6,   0xBCDC,
          0x67B7,   0xBCCF,
          0x67B8,   0xE8DB,
          0x67C1,   0xE8DE,
          0x67C3,   0xE8DA,
          0x67C4,   0xB1FA,
          0x67CF,   0xB0D8,
          0x67D0,   0xC4B3,
          0x67D1,   0xB8CC,
          0x67D2,   0xC6E2,
          0x67D3,   0xC8BE,
          0x67D4,   0xC8E1,
          0x67D8,   0xE8CF,
          0x67D9,   0xE8D4,
          0x67DA,   0xE8D6,
          0x67DC,   0xB9F1,
          0x67DD,   0xE8D8,
          0x67DE,   0xD7F5,
          0x67E0,   0xC4FB,
          0x67E2,   0xE8DC,
          0x67E5,   0xB2E9,
          0x67E9,   0xE8D1,
          0x67EC,   0xBCED,
          0x67EF,   0xBFC2,
          0x67F0,   0xE8CD,
          0x67F1,   0xD6F9,
          0x67F3,   0xC1F8,
          0x67F4,   0xB2F1,
          0x67FD,   0xE8DF,
          0x67FF,   0xCAC1,
          0x6800,   0xE8D9,
          0x6805,   0xD5A4,
          0x6807,   0xB1EA,
          0x6808,   0xD5BB,
          0x6809,   0xE8CE,
          0x680A,   0xE8D0,
          0x680B,   0xB6B0,
          0x680C,   0xE8D3,
          0x680E,   0xE8DD,
          0x680F,   0xC0B8,
          0x6811,   0xCAF7,
          0x6813,   0xCBA8,
          0x6816,   0xC6DC,
          0x6817,   0xC0F5,
          0x681D,   0xE8E9,
          0x6821,   0xD0A3,
          0x6829,   0xE8F2,
          0x682A,   0xD6EA,
          0x6832,   0xE8E0,
          0x6833,   0xE8E1,
          0x6837,   0xD1F9,
          0x6838,   0xBACB,
          0x6839,   0xB8F9,
          0x683C,   0xB8F1,
          0x683D,   0xD4D4,
          0x683E,   0xE8EF,
          0x6840,   0xE8EE,
          0x6841,   0xE8EC,
          0x6842,   0xB9F0,
          0x6843,   0xCCD2,
          0x6844,   0xE8E6,
          0x6845,   0xCEA6,
          0x6846,   0xBFF2,
          0x6848,   0xB0B8,
          0x6849,   0xE8F1,
          0x684A,   0xE8F0,
          0x684C,   0xD7C0,
          0x684E,   0xE8E4,
          0x6850,   0xCDA9,
          0x6851,   0xC9A3,
          0x6853,   0xBBB8,
          0x6854,   0xBDDB,
          0x6855,   0xE8EA,
          0x6860,   0xE8E2,
          0x6861,   0xE8E3,
          0x6862,   0xE8E5,
          0x6863,   0xB5B5,
          0x6864,   0xE8E7,
          0x6865,   0xC7C5,
          0x6866,   0xE8EB,
          0x6867,   0xE8ED,
          0x6868,   0xBDB0,
          0x6869,   0xD7AE,
          0x686B,   0xE8F8,
          0x6874,   0xE8F5,
          0x6876,   0xCDB0,
          0x6877,   0xE8F6,
          0x6881,   0xC1BA,
          0x6883,   0xE8E8,
          0x6885,   0xC3B7,
          0x6886,   0xB0F0,
          0x688F,   0xE8F4,
          0x6893,   0xE8F7,
          0x6897,   0xB9A3,
          0x68A2,   0xC9D2,
          0x68A6,   0xC3CE,
          0x68A7,   0xCEE0,
          0x68A8,   0xC0E6,
          0x68AD,   0xCBF3,
          0x68AF,   0xCCDD,
          0x68B0,   0xD0B5,
          0x68B3,   0xCAE1,
          0x68B5,   0xE8F3,
          0x68C0,   0xBCEC,
          0x68C2,   0xE8F9,
          0x68C9,   0xC3DE,
          0x68CB,   0xC6E5,
          0x68CD,   0xB9F7,
          0x68D2,   0xB0F4,
          0x68D5,   0xD7D8,
          0x68D8,   0xBCAC,
          0x68DA,   0xC5EF,
          0x68E0,   0xCCC4,
          0x68E3,   0xE9A6,
          0x68EE,   0xC9AD,
          0x68F0,   0xE9A2,
          0x68F1,   0xC0E2,
          0x68F5,   0xBFC3,
          0x68F9,   0xE8FE,
          0x68FA,   0xB9D7,
          0x68FC,   0xE8FB,
          0x6901,   0xE9A4,
          0x6905,   0xD2CE,
          0x690B,   0xE9A3,
          0x690D,   0xD6B2,
          0x690E,   0xD7B5,
          0x6910,   0xE9A7,
          0x6912,   0xBDB7,
          0x691F,   0xE8FC,
          0x6920,   0xE8FD,
          0x6924,   0xE9A1,
          0x692D,   0xCDD6,
          0x6930,   0xD2AC,
          0x6934,   0xE9B2,
          0x6939,   0xE9A9,
          0x693D,   0xB4AA,
          0x693F,   0xB4BB,
          0x6942,   0xE9AB,
          0x6954,   0xD0A8,
          0x6957,   0xE9A5,
          0x695A,   0xB3FE,
          0x695D,   0xE9AC,
          0x695E,   0xC0E3,
          0x6960,   0xE9AA,
          0x6963,   0xE9B9,
          0x6966,   0xE9B8,
          0x696B,   0xE9AE,
          0x696E,   0xE8FA,
          0x6971,   0xE9A8,
          0x6977,   0xBFAC,
          0x6978,   0xE9B1,
          0x6979,   0xE9BA,
          0x697C,   0xC2A5,
          0x6980,   0xE9AF,
          0x6982,   0xB8C5,
          0x6984,   0xE9AD,
          0x6986,   0xD3DC,
          0x6987,   0xE9B4,
          0x6988,   0xE9B5,
          0x6989,   0xE9B7,
          0x698D,   0xE9C7,
          0x6994,   0xC0C6,
          0x6995,   0xE9C5,
          0x6998,   0xE9B0,
          0x699B,   0xE9BB,
          0x699C,   0xB0F1,
          0x69A7,   0xE9BC,
          0x69A8,   0xD5A5,
          0x69AB,   0xE9BE,
          0x69AD,   0xE9BF,
          0x69B1,   0xE9C1,
          0x69B4,   0xC1F1,
          0x69B7,   0xC8B6,
          0x69BB,   0xE9BD,
          0x69C1,   0xE9C2,
          0x69CA,   0xE9C3,
          0x69CC,   0xE9B3,
          0x69CE,   0xE9B6,
          0x69D0,   0xBBB1,
          0x69D4,   0xE9C0,
          0x69DB,   0xBCF7,
          0x69DF,   0xE9C4,
          0x69E0,   0xE9C6,
          0x69ED,   0xE9CA,
          0x69F2,   0xE9CE,
          0x69FD,   0xB2DB,
          0x69FF,   0xE9C8,
          0x6A0A,   0xB7AE,
          0x6A17,   0xE9CB,
          0x6A18,   0xE9CC,
          0x6A1F,   0xD5C1,
          0x6A21,   0xC4A3,
          0x6A28,   0xE9D8,
          0x6A2A,   0xBAE1,
          0x6A2F,   0xE9C9,
          0x6A31,   0xD3A3,
          0x6A35,   0xE9D4,
          0x6A3D,   0xE9D7,
          0x6A3E,   0xE9D0,
          0x6A44,   0xE9CF,
          0x6A47,   0xC7C1,
          0x6A50,   0xE9D2,
          0x6A58,   0xE9D9,
          0x6A59,   0xB3C8,
          0x6A5B,   0xE9D3,
          0x6A61,   0xCFF0,
          0x6A65,   0xE9CD,
          0x6A71,   0xB3F7,
          0x6A79,   0xE9D6,
          0x6A7C,   0xE9DA,
          0x6A80,   0xCCB4,
          0x6A84,   0xCFAD,
          0x6A8E,   0xE9D5,
          0x6A90,   0xE9DC,
          0x6A91,   0xE9DB,
          0x6A97,   0xE9DE,
          0x6AA0,   0xE9D1,
          0x6AA9,   0xE9DD,
          0x6AAB,   0xE9DF,
          0x6AAC,   0xC3CA,
          0x6B20,   0xC7B7,
          0x6B21,   0xB4CE,
          0x6B22,   0xBBB6,
          0x6B23,   0xD0C0,
          0x6B24,   0xECA3,
          0x6B27,   0xC5B7,
          0x6B32,   0xD3FB,
          0x6B37,   0xECA4,
          0x6B39,   0xECA5,
          0x6B3A,   0xC6DB,
          0x6B3E,   0xBFEE,
          0x6B43,   0xECA6,
          0x6B46,   0xECA7,
          0x6B47,   0xD0AA,
          0x6B49,   0xC7B8,
          0x6B4C,   0xB8E8,
          0x6B59,   0xECA8,
          0x6B62,   0xD6B9,
          0x6B63,   0xD5FD,
          0x6B64,   0xB4CB,
          0x6B65,   0xB2BD,
          0x6B66,   0xCEE4,
          0x6B67,   0xC6E7,
          0x6B6A,   0xCDE1,
          0x6B79,   0xB4F5,
          0x6B7B,   0xCBC0,
          0x6B7C,   0xBCDF,
          0x6B81,   0xE9E2,
          0x6B82,   0xE9E3,
          0x6B83,   0xD1EA,
          0x6B84,   0xE9E5,
          0x6B86,   0xB4F9,
          0x6B87,   0xE9E4,
          0x6B89,   0xD1B3,
          0x6B8A,   0xCAE2,
          0x6B8B,   0xB2D0,
          0x6B8D,   0xE9E8,
          0x6B92,   0xE9E6,
          0x6B93,   0xE9E7,
          0x6B96,   0xD6B3,
          0x6B9A,   0xE9E9,
          0x6B9B,   0xE9EA,
          0x6BA1,   0xE9EB,
          0x6BAA,   0xE9EC,
          0x6BB3,   0xECAF,
          0x6BB4,   0xC5B9,
          0x6BB5,   0xB6CE,
          0x6BB7,   0xD2F3,
          0x6BBF,   0xB5EE,
          0x6BC1,   0xBBD9,
          0x6BC2,   0xECB1,
          0x6BC5,   0xD2E3,
          0x6BCB,   0xCEE3,
          0x6BCD,   0xC4B8,
          0x6BCF,   0xC3BF,
          0x6BD2,   0xB6BE,
          0x6BD3,   0xD8B9,
          0x6BD4,   0xB1C8,
          0x6BD5,   0xB1CF,
          0x6BD6,   0xB1D1,
          0x6BD7,   0xC5FE,
          0x6BD9,   0xB1D0,
          0x6BDB,   0xC3AB,
          0x6BE1,   0xD5B1,
          0x6BEA,   0xEBA4,
          0x6BEB,   0xBAC1,
          0x6BEF,   0xCCBA,
          0x6BF3,   0xEBA5,
          0x6BF5,   0xEBA7,
          0x6BF9,   0xEBA8,
          0x6BFD,   0xEBA6,
          0x6C05,   0xEBA9,
          0x6C06,   0xEBAB,
          0x6C07,   0xEBAA,
          0x6C0D,   0xEBAC,
          0x6C0F,   0xCACF,
          0x6C10,   0xD8B5,
          0x6C11,   0xC3F1,
          0x6C13,   0xC3A5,
          0x6C14,   0xC6F8,
          0x6C15,   0xEBAD,
          0x6C16,   0xC4CA,
          0x6C18,   0xEBAE,
          0x6C19,   0xEBAF,
          0x6C1A,   0xEBB0,
          0x6C1B,   0xB7D5,
          0x6C1F,   0xB7FA,
          0x6C21,   0xEBB1,
          0x6C22,   0xC7E2,
          0x6C24,   0xEBB3,
          0x6C26,   0xBAA4,
          0x6C27,   0xD1F5,
          0x6C28,   0xB0B1,
          0x6C29,   0xEBB2,
          0x6C2A,   0xEBB4,
          0x6C2E,   0xB5AA,
          0x6C2F,   0xC2C8,
          0x6C30,   0xC7E8,
          0x6C32,   0xEBB5,
          0x6C34,   0xCBAE,
          0x6C35,   0xE3DF,
          0x6C38,   0xD3C0,
          0x6C3D,   0xD9DB,
          0x6C40,   0xCDA1,
          0x6C41,   0xD6AD,
          0x6C42,   0xC7F3,
          0x6C46,   0xD9E0,
          0x6C47,   0xBBE3,
          0x6C49,   0xBABA,
          0x6C4A,   0xE3E2,
          0x6C50,   0xCFAB,
          0x6C54,   0xE3E0,
          0x6C55,   0xC9C7,
          0x6C57,   0xBAB9,
          0x6C5B,   0xD1B4,
          0x6C5C,   0xE3E1,
          0x6C5D,   0xC8EA,
          0x6C5E,   0xB9AF,
          0x6C5F,   0xBDAD,
          0x6C60,   0xB3D8,
          0x6C61,   0xCEDB,
          0x6C64,   0xCCC0,
          0x6C68,   0xE3E8,
          0x6C69,   0xE3E9,
          0x6C6A,   0xCDF4,
          0x6C70,   0xCCAD,
          0x6C72,   0xBCB3,
          0x6C74,   0xE3EA,
          0x6C76,   0xE3EB,
          0x6C79,   0xD0DA,
          0x6C7D,   0xC6FB,
          0x6C7E,   0xB7DA,
          0x6C81,   0xC7DF,
          0x6C82,   0xD2CA,
          0x6C83,   0xCED6,
          0x6C85,   0xE3E4,
          0x6C86,   0xE3EC,
          0x6C88,   0xC9F2,
          0x6C89,   0xB3C1,
          0x6C8C,   0xE3E7,
          0x6C8F,   0xC6E3,
          0x6C90,   0xE3E5,
          0x6C93,   0xEDB3,
          0x6C94,   0xE3E6,
          0x6C99,   0xC9B3,
          0x6C9B,   0xC5E6,
          0x6C9F,   0xB9B5,
          0x6CA1,   0xC3BB,
          0x6CA3,   0xE3E3,
          0x6CA4,   0xC5BD,
          0x6CA5,   0xC1A4,
          0x6CA6,   0xC2D9,
          0x6CA7,   0xB2D7,
          0x6CA9,   0xE3ED,
          0x6CAA,   0xBBA6,
          0x6CAB,   0xC4AD,
          0x6CAD,   0xE3F0,
          0x6CAE,   0xBEDA,
          0x6CB1,   0xE3FB,
          0x6CB2,   0xE3F5,
          0x6CB3,   0xBAD3,
          0x6CB8,   0xB7D0,
          0x6CB9,   0xD3CD,
          0x6CBB,   0xD6CE,
          0x6CBC,   0xD5D3,
          0x6CBD,   0xB9C1,
          0x6CBE,   0xD5B4,
          0x6CBF,   0xD1D8,
          0x6CC4,   0xD0B9,
          0x6CC5,   0xC7F6,
          0x6CC9,   0xC8AA,
          0x6CCA,   0xB2B4,
          0x6CCC,   0xC3DA,
          0x6CD0,   0xE3EE,
          0x6CD3,   0xE3FC,
          0x6CD4,   0xE3EF,
          0x6CD5,   0xB7A8,
          0x6CD6,   0xE3F7,
          0x6CD7,   0xE3F4,
          0x6CDB,   0xB7BA,
          0x6CDE,   0xC5A2,
          0x6CE0,   0xE3F6,
          0x6CE1,   0xC5DD,
          0x6CE2,   0xB2A8,
          0x6CE3,   0xC6FC,
          0x6CE5,   0xC4E0,
          0x6CE8,   0xD7A2,
          0x6CEA,   0xC0E1,
          0x6CEB,   0xE3F9,
          0x6CEE,   0xE3FA,
          0x6CEF,   0xE3FD,
          0x6CF0,   0xCCA9,
          0x6CF1,   0xE3F3,
          0x6CF3,   0xD3BE,
          0x6CF5,   0xB1C3,
          0x6CF6,   0xEDB4,
          0x6CF7,   0xE3F1,
          0x6CF8,   0xE3F2,
          0x6CFA,   0xE3F8,
          0x6CFB,   0xD0BA,
          0x6CFC,   0xC6C3,
          0x6CFD,   0xD4F3,
          0x6CFE,   0xE3FE,
          0x6D01,   0xBDE0,
          0x6D04,   0xE4A7,
          0x6D07,   0xE4A6,
          0x6D0B,   0xD1F3,
          0x6D0C,   0xE4A3,
          0x6D0E,   0xE4A9,
          0x6D12,   0xC8F7,
          0x6D17,   0xCFB4,
          0x6D19,   0xE4A8,
          0x6D1A,   0xE4AE,
          0x6D1B,   0xC2E5,
          0x6D1E,   0xB6B4,
          0x6D25,   0xBDF2,
          0x6D27,   0xE4A2,
          0x6D2A,   0xBAE9,
          0x6D2B,   0xE4AA,
          0x6D2E,   0xE4AC,
          0x6D31,   0xB6FD,
          0x6D32,   0xD6DE,
          0x6D33,   0xE4B2,
          0x6D35,   0xE4AD,
          0x6D39,   0xE4A1,
          0x6D3B,   0xBBEE,
          0x6D3C,   0xCDDD,
          0x6D3D,   0xC7A2,
          0x6D3E,   0xC5C9,
          0x6D41,   0xC1F7,
          0x6D43,   0xE4A4,
          0x6D45,   0xC7B3,
          0x6D46,   0xBDAC,
          0x6D47,   0xBDBD,
          0x6D48,   0xE4A5,
          0x6D4A,   0xD7C7,
          0x6D4B,   0xB2E2,
          0x6D4D,   0xE4AB,
          0x6D4E,   0xBCC3,
          0x6D4F,   0xE4AF,
          0x6D51,   0xBBEB,
          0x6D52,   0xE4B0,
          0x6D53,   0xC5A8,
          0x6D54,   0xE4B1,
          0x6D59,   0xD5E3,
          0x6D5A,   0xBFA3,
          0x6D5C,   0xE4BA,
          0x6D5E,   0xE4B7,
          0x6D60,   0xE4BB,
          0x6D63,   0xE4BD,
          0x6D66,   0xC6D6,
          0x6D69,   0xBAC6,
          0x6D6A,   0xC0CB,
          0x6D6E,   0xB8A1,
          0x6D6F,   0xE4B4,
          0x6D74,   0xD4A1,
          0x6D77,   0xBAA3,
          0x6D78,   0xBDFE,
          0x6D7C,   0xE4BC,
          0x6D82,   0xCDBF,
          0x6D85,   0xC4F9,
          0x6D88,   0xCFFB,
          0x6D89,   0xC9E6,
          0x6D8C,   0xD3BF,
          0x6D8E,   0xCFD1,
          0x6D91,   0xE4B3,
          0x6D93,   0xE4B8,
          0x6D94,   0xE4B9,
          0x6D95,   0xCCE9,
          0x6D9B,   0xCCCE,
          0x6D9D,   0xC0D4,
          0x6D9E,   0xE4B5,
          0x6D9F,   0xC1B0,
          0x6DA0,   0xE4B6,
          0x6DA1,   0xCED0,
          0x6DA3,   0xBBC1,
          0x6DA4,   0xB5D3,
          0x6DA6,   0xC8F3,
          0x6DA7,   0xBDA7,
          0x6DA8,   0xD5C7,
          0x6DA9,   0xC9AC,
          0x6DAA,   0xB8A2,
          0x6DAB,   0xE4CA,
          0x6DAE,   0xE4CC,
          0x6DAF,   0xD1C4,
          0x6DB2,   0xD2BA,
          0x6DB5,   0xBAAD,
          0x6DB8,   0xBAD4,
          0x6DBF,   0xE4C3,
          0x6DC0,   0xB5ED,
          0x6DC4,   0xD7CD,
          0x6DC5,   0xE4C0,
          0x6DC6,   0xCFFD,
          0x6DC7,   0xE4BF,
          0x6DCB,   0xC1DC,
          0x6DCC,   0xCCCA,
          0x6DD1,   0xCAE7,
          0x6DD6,   0xC4D7,
          0x6DD8,   0xCCD4,
          0x6DD9,   0xE4C8,
          0x6DDD,   0xE4C7,
          0x6DDE,   0xE4C1,
          0x6DE0,   0xE4C4,
          0x6DE1,   0xB5AD,
          0x6DE4,   0xD3D9,
          0x6DE6,   0xE4C6,
          0x6DEB,   0xD2F9,
          0x6DEC,   0xB4E3,
          0x6DEE,   0xBBB4,
          0x6DF1,   0xC9EE,
          0x6DF3,   0xB4BE,
          0x6DF7,   0xBBEC,
          0x6DF9,   0xD1CD,
          0x6DFB,   0xCCED,
          0x6DFC,   0xEDB5,
          0x6E05,   0xC7E5,
          0x6E0A,   0xD4A8,
          0x6E0C,   0xE4CB,
          0x6E0D,   0xD7D5,
          0x6E0E,   0xE4C2,
          0x6E10,   0xBDA5,
          0x6E11,   0xE4C5,
          0x6E14,   0xD3E6,
          0x6E16,   0xE4C9,
          0x6E17,   0xC9F8,
          0x6E1A,   0xE4BE,
          0x6E1D,   0xD3E5,
          0x6E20,   0xC7FE,
          0x6E21,   0xB6C9,
          0x6E23,   0xD4FC,
          0x6E24,   0xB2B3,
          0x6E25,   0xE4D7,
          0x6E29,   0xCEC2,
          0x6E2B,   0xE4CD,
          0x6E2D,   0xCEBC,
          0x6E2F,   0xB8DB,
          0x6E32,   0xE4D6,
          0x6E34,   0xBFCA,
          0x6E38,   0xD3CE,
          0x6E3A,   0xC3EC,
          0x6E43,   0xC5C8,
          0x6E44,   0xE4D8,
          0x6E4D,   0xCDC4,
          0x6E4E,   0xE4CF,
          0x6E53,   0xE4D4,
          0x6E54,   0xE4D5,
          0x6E56,   0xBAFE,
          0x6E58,   0xCFE6,
          0x6E5B,   0xD5BF,
          0x6E5F,   0xE4D2,
          0x6E6B,   0xE4D0,
          0x6E6E,   0xE4CE,
          0x6E7E,   0xCDE5,
          0x6E7F,   0xCAAA,
          0x6E83,   0xC0A3,
          0x6E85,   0xBDA6,
          0x6E86,   0xE4D3,
          0x6E89,   0xB8C8,
          0x6E8F,   0xE4E7,
          0x6E90,   0xD4B4,
          0x6E98,   0xE4DB,
          0x6E9C,   0xC1EF,
          0x6E9F,   0xE4E9,
          0x6EA2,   0xD2E7,
          0x6EA5,   0xE4DF,
          0x6EA7,   0xE4E0,
          0x6EAA,   0xCFAA,
          0x6EAF,   0xCBDD,
          0x6EB1,   0xE4DA,
          0x6EB2,   0xE4D1,
          0x6EB4,   0xE4E5,
          0x6EB6,   0xC8DC,
          0x6EB7,   0xE4E3,
          0x6EBA,   0xC4E7,
          0x6EBB,   0xE4E2,
          0x6EBD,   0xE4E1,
          0x6EC1,   0xB3FC,
          0x6EC2,   0xE4E8,
          0x6EC7,   0xB5E1,
          0x6ECB,   0xD7CC,
          0x6ECF,   0xE4E6,
          0x6ED1,   0xBBAC,
          0x6ED3,   0xD7D2,
          0x6ED4,   0xCCCF,
          0x6ED5,   0xEBF8,
          0x6ED7,   0xE4E4,
          0x6EDA,   0xB9F6,
          0x6EDE,   0xD6CD,
          0x6EDF,   0xE4D9,
          0x6EE0,   0xE4DC,
          0x6EE1,   0xC2FA,
          0x6EE2,   0xE4DE,
          0x6EE4,   0xC2CB,
          0x6EE5,   0xC0C4,
          0x6EE6,   0xC2D0,
          0x6EE8,   0xB1F5,
          0x6EE9,   0xCCB2,
          0x6EF4,   0xB5CE,
          0x6EF9,   0xE4EF,
          0x6F02,   0xC6AF,
          0x6F06,   0xC6E1,
          0x6F09,   0xE4F5,
          0x6F0F,   0xC2A9,
          0x6F13,   0xC0EC,
          0x6F14,   0xD1DD,
          0x6F15,   0xE4EE,
          0x6F20,   0xC4AE,
          0x6F24,   0xE4ED,
          0x6F29,   0xE4F6,
          0x6F2A,   0xE4F4,
          0x6F2B,   0xC2FE,
          0x6F2D,   0xE4DD,
          0x6F2F,   0xE4F0,
          0x6F31,   0xCAFE,
          0x6F33,   0xD5C4,
          0x6F36,   0xE4F1,
          0x6F3E,   0xD1FA,
          0x6F46,   0xE4EB,
          0x6F47,   0xE4EC,
          0x6F4B,   0xE4F2,
          0x6F4D,   0xCEAB,
          0x6F58,   0xC5CB,
          0x6F5C,   0xC7B1,
          0x6F5E,   0xC2BA,
          0x6F62,   0xE4EA,
          0x6F66,   0xC1CA,
          0x6F6D,   0xCCB6,
          0x6F6E,   0xB3B1,
          0x6F72,   0xE4FB,
          0x6F74,   0xE4F3,
          0x6F78,   0xE4FA,
          0x6F7A,   0xE4FD,
          0x6F7C,   0xE4FC,
          0x6F84,   0xB3CE,
          0x6F88,   0xB3BA,
          0x6F89,   0xE4F7,
          0x6F8C,   0xE4F9,
          0x6F8D,   0xE4F8,
          0x6F8E,   0xC5EC,
          0x6F9C,   0xC0BD,
          0x6FA1,   0xD4E8,
          0x6FA7,   0xE5A2,
          0x6FB3,   0xB0C4,
          0x6FB6,   0xE5A4,
          0x6FB9,   0xE5A3,
          0x6FC0,   0xBCA4,
          0x6FC2,   0xE5A5,
          0x6FC9,   0xE5A1,
          0x6FD1,   0xE4FE,
          0x6FD2,   0xB1F4,
          0x6FDE,   0xE5A8,
          0x6FE0,   0xE5A9,
          0x6FE1,   0xE5A6,
          0x6FEE,   0xE5A7,
          0x6FEF,   0xE5AA,
          0x7011,   0xC6D9,
          0x701A,   0xE5AB,
          0x701B,   0xE5AD,
          0x7023,   0xE5AC,
          0x7035,   0xE5AF,
          0x7039,   0xE5AE,
          0x704C,   0xB9E0,
          0x704F,   0xE5B0,
          0x705E,   0xE5B1,
          0x706B,   0xBBF0,
          0x706C,   0xECE1,
          0x706D,   0xC3F0,
          0x706F,   0xB5C6,
          0x7070,   0xBBD2,
          0x7075,   0xC1E9,
          0x7076,   0xD4EE,
          0x7078,   0xBEC4,
          0x707C,   0xD7C6,
          0x707E,   0xD4D6,
          0x707F,   0xB2D3,
          0x7080,   0xECBE,
          0x7085,   0xEAC1,
          0x7089,   0xC2AF,
          0x708A,   0xB4B6,
          0x708E,   0xD1D7,
          0x7092,   0xB3B4,
          0x7094,   0xC8B2,
          0x7095,   0xBFBB,
          0x7096,   0xECC0,
          0x7099,   0xD6CB,
          0x709C,   0xECBF,
          0x709D,   0xECC1,
          0x70AB,   0xECC5,
          0x70AC,   0xBEE6,
          0x70AD,   0xCCBF,
          0x70AE,   0xC5DA,
          0x70AF,   0xBEBC,
          0x70B1,   0xECC6,
          0x70B3,   0xB1FE,
          0x70B7,   0xECC4,
          0x70B8,   0xD5A8,
          0x70B9,   0xB5E3,
          0x70BB,   0xECC2,
          0x70BC,   0xC1B6,
          0x70BD,   0xB3E3,
          0x70C0,   0xECC3,
          0x70C1,   0xCBB8,
          0x70C2,   0xC0C3,
          0x70C3,   0xCCFE,
          0x70C8,   0xC1D2,
          0x70CA,   0xECC8,
          0x70D8,   0xBAE6,
          0x70D9,   0xC0D3,
          0x70DB,   0xD6F2,
          0x70DF,   0xD1CC,
          0x70E4,   0xBFBE,
          0x70E6,   0xB7B3,
          0x70E7,   0xC9D5,
          0x70E8,   0xECC7,
          0x70E9,   0xBBE2,
          0x70EB,   0xCCCC,
          0x70EC,   0xBDFD,
          0x70ED,   0xC8C8,
          0x70EF,   0xCFA9,
          0x70F7,   0xCDE9,
          0x70F9,   0xC5EB,
          0x70FD,   0xB7E9,
          0x7109,   0xD1C9,
          0x710A,   0xBAB8,
          0x7110,   0xECC9,
          0x7113,   0xECCA,
          0x7115,   0xBBC0,
          0x7116,   0xECCB,
          0x7118,   0xECE2,
          0x7119,   0xB1BA,
          0x711A,   0xB7D9,
          0x7126,   0xBDB9,
          0x712F,   0xECCC,
          0x7130,   0xD1E6,
          0x7131,   0xECCD,
          0x7136,   0xC8BB,
          0x7145,   0xECD1,
          0x714A,   0xECD3,
          0x714C,   0xBBCD,
          0x714E,   0xBCE5,
          0x715C,   0xECCF,
          0x715E,   0xC9B7,
          0x7164,   0xC3BA,
          0x7166,   0xECE3,
          0x7167,   0xD5D5,
          0x7168,   0xECD0,
          0x716E,   0xD6F3,
          0x7172,   0xECD2,
          0x7173,   0xECCE,
          0x7178,   0xECD4,
          0x717A,   0xECD5,
          0x717D,   0xC9BF,
          0x7184,   0xCFA8,
          0x718A,   0xD0DC,
          0x718F,   0xD1AC,
          0x7194,   0xC8DB,
          0x7198,   0xECD6,
          0x7199,   0xCEF5,
          0x719F,   0xCAEC,
          0x71A0,   0xECDA,
          0x71A8,   0xECD9,
          0x71AC,   0xB0BE,
          0x71B3,   0xECD7,
          0x71B5,   0xECD8,
          0x71B9,   0xECE4,
          0x71C3,   0xC8BC,
          0x71CE,   0xC1C7,
          0x71D4,   0xECDC,
          0x71D5,   0xD1E0,
          0x71E0,   0xECDB,
          0x71E5,   0xD4EF,
          0x71E7,   0xECDD,
          0x71EE,   0xDBC6,
          0x71F9,   0xECDE,
          0x7206,   0xB1AC,
          0x721D,   0xECDF,
          0x7228,   0xECE0,
          0x722A,   0xD7A6,
          0x722C,   0xC5C0,
          0x7230,   0xEBBC,
          0x7231,   0xB0AE,
          0x7235,   0xBEF4,
          0x7236,   0xB8B8,
          0x7237,   0xD2AF,
          0x7238,   0xB0D6,
          0x7239,   0xB5F9,
          0x723B,   0xD8B3,
          0x723D,   0xCBAC,
          0x723F,   0xE3DD,
          0x7247,   0xC6AC,
          0x7248,   0xB0E6,
          0x724C,   0xC5C6,
          0x724D,   0xEBB9,
          0x7252,   0xEBBA,
          0x7256,   0xEBBB,
          0x7259,   0xD1C0,
          0x725B,   0xC5A3,
          0x725D,   0xEAF2,
          0x725F,   0xC4B2,
          0x7261,   0xC4B5,
          0x7262,   0xC0CE,
          0x7266,   0xEAF3,
          0x7267,   0xC4C1,
          0x7269,   0xCEEF,
          0x726E,   0xEAF0,
          0x726F,   0xEAF4,
          0x7272,   0xC9FC,
          0x7275,   0xC7A3,
          0x7279,   0xCCD8,
          0x727A,   0xCEFE,
          0x727E,   0xEAF5,
          0x727F,   0xEAF6,
          0x7280,   0xCFAC,
          0x7281,   0xC0E7,
          0x7284,   0xEAF7,
          0x728A,   0xB6BF,
          0x728B,   0xEAF8,
          0x728D,   0xEAF9,
          0x728F,   0xEAFA,
          0x7292,   0xEAFB,
          0x729F,   0xEAF1,
          0x72AC,   0xC8AE,
          0x72AD,   0xE1EB,
          0x72AF,   0xB7B8,
          0x72B0,   0xE1EC,
          0x72B4,   0xE1ED,
          0x72B6,   0xD7B4,
          0x72B7,   0xE1EE,
          0x72B8,   0xE1EF,
          0x72B9,   0xD3CC,
          0x72C1,   0xE1F1,
          0x72C2,   0xBFF1,
          0x72C3,   0xE1F0,
          0x72C4,   0xB5D2,
          0x72C8,   0xB1B7,
          0x72CD,   0xE1F3,
          0x72CE,   0xE1F2,
          0x72D0,   0xBAFC,
          0x72D2,   0xE1F4,
          0x72D7,   0xB9B7,
          0x72D9,   0xBED1,
          0x72DE,   0xC4FC,
          0x72E0,   0xBADD,
          0x72E1,   0xBDC6,
          0x72E8,   0xE1F5,
          0x72E9,   0xE1F7,
          0x72EC,   0xB6C0,
          0x72ED,   0xCFC1,
          0x72EE,   0xCAA8,
          0x72EF,   0xE1F6,
          0x72F0,   0xD5F8,
          0x72F1,   0xD3FC,
          0x72F2,   0xE1F8,
          0x72F3,   0xE1FC,
          0x72F4,   0xE1F9,
          0x72F7,   0xE1FA,
          0x72F8,   0xC0EA,
          0x72FA,   0xE1FE,
          0x72FB,   0xE2A1,
          0x72FC,   0xC0C7,
          0x7301,   0xE1FB,
          0x7303,   0xE1FD,
          0x730A,   0xE2A5,
          0x730E,   0xC1D4,
          0x7313,   0xE2A3,
          0x7315,   0xE2A8,
          0x7316,   0xB2FE,
          0x7317,   0xE2A2,
          0x731B,   0xC3CD,
          0x731C,   0xB2C2,
          0x731D,   0xE2A7,
          0x731E,   0xE2A6,
          0x7321,   0xE2A4,
          0x7322,   0xE2A9,
          0x7325,   0xE2AB,
          0x7329,   0xD0C9,
          0x732A,   0xD6ED,
          0x732B,   0xC3A8,
          0x732C,   0xE2AC,
          0x732E,   0xCFD7,
          0x7331,   0xE2AE,
          0x7334,   0xBAEF,
          0x7337,   0xE9E0,
          0x7338,   0xE2AD,
          0x7339,   0xE2AA,
          0x733E,   0xBBAB,
          0x733F,   0xD4B3,
          0x734D,   0xE2B0,
          0x7350,   0xE2AF,
          0x7352,   0xE9E1,
          0x7357,   0xE2B1,
          0x7360,   0xE2B2,
          0x736C,   0xE2B3,
          0x736D,   0xCCA1,
          0x736F,   0xE2B4,
          0x737E,   0xE2B5,
          0x7384,   0xD0FE,
          0x7387,   0xC2CA,
          0x7389,   0xD3F1,
          0x738B,   0xCDF5,
          0x738E,   0xE7E0,
          0x7391,   0xE7E1,
          0x7396,   0xBEC1,
          0x739B,   0xC2EA,
          0x739F,   0xE7E4,
          0x73A2,   0xE7E3,
          0x73A9,   0xCDE6,
          0x73AB,   0xC3B5,
          0x73AE,   0xE7E2,
          0x73AF,   0xBBB7,
          0x73B0,   0xCFD6,
          0x73B2,   0xC1E1,
          0x73B3,   0xE7E9,
          0x73B7,   0xE7E8,
          0x73BA,   0xE7F4,
          0x73BB,   0xB2A3,
          0x73C0,   0xE7EA,
          0x73C2,   0xE7E6,
          0x73C8,   0xE7EC,
          0x73C9,   0xE7EB,
          0x73CA,   0xC9BA,
          0x73CD,   0xD5E4,
          0x73CF,   0xE7E5,
          0x73D0,   0xB7A9,
          0x73D1,   0xE7E7,
          0x73D9,   0xE7EE,
          0x73DE,   0xE7F3,
          0x73E0,   0xD6E9,
          0x73E5,   0xE7ED,
          0x73E7,   0xE7F2,
          0x73E9,   0xE7F1,
          0x73ED,   0xB0E0,
          0x73F2,   0xE7F5,
          0x7403,   0xC7F2,
          0x7405,   0xC0C5,
          0x7406,   0xC0ED,
          0x7409,   0xC1F0,
          0x740A,   0xE7F0,
          0x740F,   0xE7F6,
          0x7410,   0xCBF6,
          0x741A,   0xE8A2,
          0x741B,   0xE8A1,
          0x7422,   0xD7C1,
          0x7425,   0xE7FA,
          0x7426,   0xE7F9,
          0x7428,   0xE7FB,
          0x742A,   0xE7F7,
          0x742C,   0xE7FE,
          0x742E,   0xE7FD,
          0x7430,   0xE7FC,
          0x7433,   0xC1D5,
          0x7434,   0xC7D9,
          0x7435,   0xC5FD,
          0x7436,   0xC5C3,
          0x743C,   0xC7ED,
          0x7441,   0xE8A3,
          0x7455,   0xE8A6,
          0x7457,   0xE8A5,
          0x7459,   0xE8A7,
          0x745A,   0xBAF7,
          0x745B,   0xE7F8,
          0x745C,   0xE8A4,
          0x745E,   0xC8F0,
          0x745F,   0xC9AA,
          0x746D,   0xE8A9,
          0x7470,   0xB9E5,
          0x7476,   0xD1FE,
          0x7477,   0xE8A8,
          0x747E,   0xE8AA,
          0x7480,   0xE8AD,
          0x7481,   0xE8AE,
          0x7483,   0xC1A7,
          0x7487,   0xE8AF,
          0x748B,   0xE8B0,
          0x748E,   0xE8AC,
          0x7490,   0xE8B4,
          0x749C,   0xE8AB,
          0x749E,   0xE8B1,
          0x74A7,   0xE8B5,
          0x74A8,   0xE8B2,
          0x74A9,   0xE8B3,
          0x74BA,   0xE8B7,
          0x74D2,   0xE8B6,
          0x74DC,   0xB9CF,
          0x74DE,   0xF0AC,
          0x74E0,   0xF0AD,
          0x74E2,   0xC6B0,
          0x74E3,   0xB0EA,
          0x74E4,   0xC8BF,
          0x74E6,   0xCDDF,
          0x74EE,   0xCECD,
          0x74EF,   0xEAB1,
          0x74F4,   0xEAB2,
          0x74F6,   0xC6BF,
          0x74F7,   0xB4C9,
          0x74FF,   0xEAB3,
          0x7504,   0xD5E7,
          0x750D,   0xDDF9,
          0x750F,   0xEAB4,
          0x7511,   0xEAB5,
          0x7513,   0xEAB6,
          0x7518,   0xB8CA,
          0x7519,   0xDFB0,
          0x751A,   0xC9F5,
          0x751C,   0xCCF0,
          0x751F,   0xC9FA,
          0x7525,   0xC9FB,
          0x7528,   0xD3C3,
          0x7529,   0xCBA6,
          0x752B,   0xB8A6,
          0x752C,   0xF0AE,
          0x752D,   0xB1C2,
          0x752F,   0xE5B8,
          0x7530,   0xCCEF,
          0x7531,   0xD3C9,
          0x7532,   0xBCD7,
          0x7533,   0xC9EA,
          0x7535,   0xB5E7,
          0x7537,   0xC4D0,
          0x7538,   0xB5E9,
          0x753A,   0xEEAE,
          0x753B,   0xBBAD,
          0x753E,   0xE7DE,
          0x7540,   0xEEAF,
          0x7545,   0xB3A9,
          0x7548,   0xEEB2,
          0x754B,   0xEEB1,
          0x754C,   0xBDE7,
          0x754E,   0xEEB0,
          0x754F,   0xCEB7,
          0x7554,   0xC5CF,
          0x7559,   0xC1F4,
          0x755A,   0xDBCE,
          0x755B,   0xEEB3,
          0x755C,   0xD0F3,
          0x7565,   0xC2D4,
          0x7566,   0xC6E8,
          0x756A,   0xB7AC,
          0x7572,   0xEEB4,
          0x7574,   0xB3EB,
          0x7578,   0xBBFB,
          0x7579,   0xEEB5,
          0x757F,   0xE7DC,
          0x7583,   0xEEB6,
          0x7586,   0xBDAE,
          0x758B,   0xF1E2,
          0x758F,   0xCAE8,
          0x7591,   0xD2C9,
          0x7592,   0xF0DA,
          0x7594,   0xF0DB,
          0x7596,   0xF0DC,
          0x7597,   0xC1C6,
          0x7599,   0xB8ED,
          0x759A,   0xBECE,
          0x759D,   0xF0DE,
          0x759F,   0xC5B1,
          0x75A0,   0xF0DD,
          0x75A1,   0xD1F1,
          0x75A3,   0xF0E0,
          0x75A4,   0xB0CC,
          0x75A5,   0xBDEA,
          0x75AB,   0xD2DF,
          0x75AC,   0xF0DF,
          0x75AE,   0xB4AF,
          0x75AF,   0xB7E8,
          0x75B0,   0xF0E6,
          0x75B1,   0xF0E5,
          0x75B2,   0xC6A3,
          0x75B3,   0xF0E1,
          0x75B4,   0xF0E2,
          0x75B5,   0xB4C3,
          0x75B8,   0xF0E3,
          0x75B9,   0xD5EE,
          0x75BC,   0xCCDB,
          0x75BD,   0xBED2,
          0x75BE,   0xBCB2,
          0x75C2,   0xF0E8,
          0x75C3,   0xF0E7,
          0x75C4,   0xF0E4,
          0x75C5,   0xB2A1,
          0x75C7,   0xD6A2,
          0x75C8,   0xD3B8,
          0x75C9,   0xBEB7,
          0x75CA,   0xC8AC,
          0x75CD,   0xF0EA,
          0x75D2,   0xD1F7,
          0x75D4,   0xD6CC,
          0x75D5,   0xBADB,
          0x75D6,   0xF0E9,
          0x75D8,   0xB6BB,
          0x75DB,   0xCDB4,
          0x75DE,   0xC6A6,
          0x75E2,   0xC1A1,
          0x75E3,   0xF0EB,
          0x75E4,   0xF0EE,
          0x75E6,   0xF0ED,
          0x75E7,   0xF0F0,
          0x75E8,   0xF0EC,
          0x75EA,   0xBBBE,
          0x75EB,   0xF0EF,
          0x75F0,   0xCCB5,
          0x75F1,   0xF0F2,
          0x75F4,   0xB3D5,
          0x75F9,   0xB1D4,
          0x75FC,   0xF0F3,
          0x75FF,   0xF0F4,
          0x7600,   0xF0F6,
          0x7601,   0xB4E1,
          0x7603,   0xF0F1,
          0x7605,   0xF0F7,
          0x760A,   0xF0FA,
          0x760C,   0xF0F8,
          0x7610,   0xF0F5,
          0x7615,   0xF0FD,
          0x7617,   0xF0F9,
          0x7618,   0xF0FC,
          0x7619,   0xF0FE,
          0x761B,   0xF1A1,
          0x761F,   0xCEC1,
          0x7620,   0xF1A4,
          0x7622,   0xF1A3,
          0x7624,   0xC1F6,
          0x7625,   0xF0FB,
          0x7626,   0xCADD,
          0x7629,   0xB4F1,
          0x762A,   0xB1F1,
          0x762B,   0xCCB1,
          0x762D,   0xF1A6,
          0x7630,   0xF1A7,
          0x7633,   0xF1AC,
          0x7634,   0xD5CE,
          0x7635,   0xF1A9,
          0x7638,   0xC8B3,
          0x763C,   0xF1A2,
          0x763E,   0xF1AB,
          0x763F,   0xF1A8,
          0x7640,   0xF1A5,
          0x7643,   0xF1AA,
          0x764C,   0xB0A9,
          0x764D,   0xF1AD,
          0x7654,   0xF1AF,
          0x7656,   0xF1B1,
          0x765C,   0xF1B0,
          0x765E,   0xF1AE,
          0x7663,   0xD1A2,
          0x766B,   0xF1B2,
          0x766F,   0xF1B3,
          0x7678,   0xB9EF,
          0x767B,   0xB5C7,
          0x767D,   0xB0D7,
          0x767E,   0xB0D9,
          0x7682,   0xD4ED,
          0x7684,   0xB5C4,
          0x7686,   0xBDD4,
          0x7687,   0xBBCA,
          0x7688,   0xF0A7,
          0x768B,   0xB8DE,
          0x768E,   0xF0A8,
          0x7691,   0xB0A8,
          0x7693,   0xF0A9,
          0x7696,   0xCDEE,
          0x7699,   0xF0AA,
          0x76A4,   0xF0AB,
          0x76AE,   0xC6A4,
          0x76B1,   0xD6E5,
          0x76B2,   0xF1E4,
          0x76B4,   0xF1E5,
          0x76BF,   0xC3F3,
          0x76C2,   0xD3DB,
          0x76C5,   0xD6D1,
          0x76C6,   0xC5E8,
          0x76C8,   0xD3AF,
          0x76CA,   0xD2E6,
          0x76CD,   0xEEC1,
          0x76CE,   0xB0BB,
          0x76CF,   0xD5B5,
          0x76D0,   0xD1CE,
          0x76D1,   0xBCE0,
          0x76D2,   0xBAD0,
          0x76D4,   0xBFF8,
          0x76D6,   0xB8C7,
          0x76D7,   0xB5C1,
          0x76D8,   0xC5CC,
          0x76DB,   0xCAA2,
          0x76DF,   0xC3CB,
          0x76E5,   0xEEC2,
          0x76EE,   0xC4BF,
          0x76EF,   0xB6A2,
          0x76F1,   0xEDEC,
          0x76F2,   0xC3A4,
          0x76F4,   0xD6B1,
          0x76F8,   0xCFE0,
          0x76F9,   0xEDEF,
          0x76FC,   0xC5CE,
          0x76FE,   0xB6DC,
          0x7701,   0xCAA1,
          0x7704,   0xEDED,
          0x7707,   0xEDF0,
          0x7708,   0xEDF1,
          0x7709,   0xC3BC,
          0x770B,   0xBFB4,
          0x770D,   0xEDEE,
          0x7719,   0xEDF4,
          0x771A,   0xEDF2,
          0x771F,   0xD5E6,
          0x7720,   0xC3DF,
          0x7722,   0xEDF3,
          0x7726,   0xEDF6,
          0x7728,   0xD5A3,
          0x7729,   0xD1A3,
          0x772D,   0xEDF5,
          0x772F,   0xC3D0,
          0x7735,   0xEDF7,
          0x7736,   0xBFF4,
          0x7737,   0xBEEC,
          0x7738,   0xEDF8,
          0x773A,   0xCCF7,
          0x773C,   0xD1DB,
          0x7740,   0xD7C5,
          0x7741,   0xD5F6,
          0x7743,   0xEDFC,
          0x7747,   0xEDFB,
          0x7750,   0xEDF9,
          0x7751,   0xEDFA,
          0x775A,   0xEDFD,
          0x775B,   0xBEA6,
          0x7761,   0xCBAF,
          0x7762,   0xEEA1,
          0x7763,   0xB6BD,
          0x7765,   0xEEA2,
          0x7766,   0xC4C0,
          0x7768,   0xEDFE,
          0x776B,   0xBDDE,
          0x776C,   0xB2C7,
          0x7779,   0xB6C3,
          0x777D,   0xEEA5,
          0x777E,   0xD8BA,
          0x777F,   0xEEA3,
          0x7780,   0xEEA6,
          0x7784,   0xC3E9,
          0x7785,   0xB3F2,
          0x778C,   0xEEA7,
          0x778D,   0xEEA4,
          0x778E,   0xCFB9,
          0x7791,   0xEEA8,
          0x7792,   0xC2F7,
          0x779F,   0xEEA9,
          0x77A0,   0xEEAA,
          0x77A2,   0xDEAB,
          0x77A5,   0xC6B3,
          0x77A7,   0xC7C6,
          0x77A9,   0xD6F5,
          0x77AA,   0xB5C9,
          0x77AC,   0xCBB2,
          0x77B0,   0xEEAB,
          0x77B3,   0xCDAB,
          0x77B5,   0xEEAC,
          0x77BB,   0xD5B0,
          0x77BD,   0xEEAD,
          0x77BF,   0xF6C4,
          0x77CD,   0xDBC7,
          0x77D7,   0xB4A3,
          0x77DB,   0xC3AC,
          0x77DC,   0xF1E6,
          0x77E2,   0xCAB8,
          0x77E3,   0xD2D3,
          0x77E5,   0xD6AA,
          0x77E7,   0xEFF2,
          0x77E9,   0xBED8,
          0x77EB,   0xBDC3,
          0x77EC,   0xEFF3,
          0x77ED,   0xB6CC,
          0x77EE,   0xB0AB,
          0x77F3,   0xCAAF,
          0x77F6,   0xEDB6,
          0x77F8,   0xEDB7,
          0x77FD,   0xCEF9,
          0x77FE,   0xB7AF,
          0x77FF,   0xBFF3,
          0x7800,   0xEDB8,
          0x7801,   0xC2EB,
          0x7802,   0xC9B0,
          0x7809,   0xEDB9,
          0x780C,   0xC6F6,
          0x780D,   0xBFB3,
          0x7811,   0xEDBC,
          0x7812,   0xC5F8,
          0x7814,   0xD1D0,
          0x7816,   0xD7A9,
          0x7817,   0xEDBA,
          0x7818,   0xEDBB,
          0x781A,   0xD1E2,
          0x781C,   0xEDBF,
          0x781D,   0xEDC0,
          0x781F,   0xEDC4,
          0x7823,   0xEDC8,
          0x7825,   0xEDC6,
          0x7826,   0xEDCE,
          0x7827,   0xD5E8,
          0x7829,   0xEDC9,
          0x782C,   0xEDC7,
          0x782D,   0xEDBE,
          0x7830,   0xC5E9,
          0x7834,   0xC6C6,
          0x7837,   0xC9E9,
          0x7838,   0xD4D2,
          0x7839,   0xEDC1,
          0x783A,   0xEDC2,
          0x783B,   0xEDC3,
          0x783C,   0xEDC5,
          0x783E,   0xC0F9,
          0x7840,   0xB4A1,
          0x7845,   0xB9E8,
          0x7847,   0xEDD0,
          0x784C,   0xEDD1,
          0x784E,   0xEDCA,
          0x7850,   0xEDCF,
          0x7852,   0xCEF8,
          0x7855,   0xCBB6,
          0x7856,   0xEDCC,
          0x7857,   0xEDCD,
          0x785D,   0xCFF5,
          0x786A,   0xEDD2,
          0x786B,   0xC1F2,
          0x786C,   0xD3B2,
          0x786D,   0xEDCB,
          0x786E,   0xC8B7,
          0x7877,   0xBCEF,
          0x787C,   0xC5F0,
          0x7887,   0xEDD6,
          0x7889,   0xB5EF,
          0x788C,   0xC2B5,
          0x788D,   0xB0AD,
          0x788E,   0xCBE9,
          0x7891,   0xB1AE,
          0x7893,   0xEDD4,
          0x7897,   0xCDEB,
          0x7898,   0xB5E2,
          0x789A,   0xEDD5,
          0x789B,   0xEDD3,
          0x789C,   0xEDD7,
          0x789F,   0xB5FA,
          0x78A1,   0xEDD8,
          0x78A3,   0xEDD9,
          0x78A5,   0xEDDC,
          0x78A7,   0xB1CC,
          0x78B0,   0xC5F6,
          0x78B1,   0xBCEE,
          0x78B2,   0xEDDA,
          0x78B3,   0xCCBC,
          0x78B4,   0xB2EA,
          0x78B9,   0xEDDB,
          0x78BE,   0xC4EB,
          0x78C1,   0xB4C5,
          0x78C5,   0xB0F5,
          0x78C9,   0xEDDF,
          0x78CA,   0xC0DA,
          0x78CB,   0xB4E8,
          0x78D0,   0xC5CD,
          0x78D4,   0xEDDD,
          0x78D5,   0xBFC4,
          0x78D9,   0xEDDE,
          0x78E8,   0xC4A5,
          0x78EC,   0xEDE0,
          0x78F2,   0xEDE1,
          0x78F4,   0xEDE3,
          0x78F7,   0xC1D7,
          0x78FA,   0xBBC7,
          0x7901,   0xBDB8,
          0x7905,   0xEDE2,
          0x7913,   0xEDE4,
          0x791E,   0xEDE6,
          0x7924,   0xEDE5,
          0x7934,   0xEDE7,
          0x793A,   0xCABE,
          0x793B,   0xECEA,
          0x793C,   0xC0F1,
          0x793E,   0xC9E7,
          0x7940,   0xECEB,
          0x7941,   0xC6EE,
          0x7946,   0xECEC,
          0x7948,   0xC6ED,
          0x7949,   0xECED,
          0x7953,   0xECF0,
          0x7956,   0xD7E6,
          0x7957,   0xECF3,
          0x795A,   0xECF1,
          0x795B,   0xECEE,
          0x795C,   0xECEF,
          0x795D,   0xD7A3,
          0x795E,   0xC9F1,
          0x795F,   0xCBEE,
          0x7960,   0xECF4,
          0x7962,   0xECF2,
          0x7965,   0xCFE9,
          0x7967,   0xECF6,
          0x7968,   0xC6B1,
          0x796D,   0xBCC0,
          0x796F,   0xECF5,
          0x7977,   0xB5BB,
          0x7978,   0xBBF6,
          0x797A,   0xECF7,
          0x7980,   0xD9F7,
          0x7981,   0xBDFB,
          0x7984,   0xC2BB,
          0x7985,   0xECF8,
          0x798A,   0xECF9,
          0x798F,   0xB8A3,
          0x799A,   0xECFA,
          0x79A7,   0xECFB,
          0x79B3,   0xECFC,
          0x79B9,   0xD3ED,
          0x79BA,   0xD8AE,
          0x79BB,   0xC0EB,
          0x79BD,   0xC7DD,
          0x79BE,   0xBACC,
          0x79C0,   0xD0E3,
          0x79C1,   0xCBBD,
          0x79C3,   0xCDBA,
          0x79C6,   0xB8D1,
          0x79C9,   0xB1FC,
          0x79CB,   0xC7EF,
          0x79CD,   0xD6D6,
          0x79D1,   0xBFC6,
          0x79D2,   0xC3EB,
          0x79D5,   0xEFF5,
          0x79D8,   0xC3D8,
          0x79DF,   0xD7E2,
          0x79E3,   0xEFF7,
          0x79E4,   0xB3D3,
          0x79E6,   0xC7D8,
          0x79E7,   0xD1ED,
          0x79E9,   0xD6C8,
          0x79EB,   0xEFF8,
          0x79ED,   0xEFF6,
          0x79EF,   0xBBFD,
          0x79F0,   0xB3C6,
          0x79F8,   0xBDD5,
          0x79FB,   0xD2C6,
          0x79FD,   0xBBE0,
          0x7A00,   0xCFA1,
          0x7A02,   0xEFFC,
          0x7A03,   0xEFFB,
          0x7A06,   0xEFF9,
          0x7A0B,   0xB3CC,
          0x7A0D,   0xC9D4,
          0x7A0E,   0xCBB0,
          0x7A14,   0xEFFE,
          0x7A17,   0xB0DE,
          0x7A1A,   0xD6C9,
          0x7A1E,   0xEFFD,
          0x7A20,   0xB3ED,
          0x7A23,   0xF6D5,
          0x7A33,   0xCEC8,
          0x7A37,   0xF0A2,
          0x7A39,   0xF0A1,
          0x7A3B,   0xB5BE,
          0x7A3C,   0xBCDA,
          0x7A3D,   0xBBFC,
          0x7A3F,   0xB8E5,
          0x7A46,   0xC4C2,
          0x7A51,   0xF0A3,
          0x7A57,   0xCBEB,
          0x7A70,   0xF0A6,
          0x7A74,   0xD1A8,
          0x7A76,   0xBEBF,
          0x7A77,   0xC7EE,
          0x7A78,   0xF1B6,
          0x7A79,   0xF1B7,
          0x7A7A,   0xBFD5,
          0x7A7F,   0xB4A9,
          0x7A80,   0xF1B8,
          0x7A81,   0xCDBB,
          0x7A83,   0xC7D4,
          0x7A84,   0xD5AD,
          0x7A86,   0xF1B9,
          0x7A88,   0xF1BA,
          0x7A8D,   0xC7CF,
          0x7A91,   0xD2A4,
          0x7A92,   0xD6CF,
          0x7A95,   0xF1BB,
          0x7A96,   0xBDD1,
          0x7A97,   0xB4B0,
          0x7A98,   0xBEBD,
          0x7A9C,   0xB4DC,
          0x7A9D,   0xCED1,
          0x7A9F,   0xBFDF,
          0x7AA0,   0xF1BD,
          0x7AA5,   0xBFFA,
          0x7AA6,   0xF1BC,
          0x7AA8,   0xF1BF,
          0x7AAC,   0xF1BE,
          0x7AAD,   0xF1C0,
          0x7AB3,   0xF1C1,
          0x7ABF,   0xC1FE,
          0x7ACB,   0xC1A2,
          0x7AD6,   0xCAFA,
          0x7AD9,   0xD5BE,
          0x7ADE,   0xBEBA,
          0x7ADF,   0xBEB9,
          0x7AE0,   0xD5C2,
          0x7AE3,   0xBFA2,
          0x7AE5,   0xCDAF,
          0x7AE6,   0xF1B5,
          0x7AED,   0xBDDF,
          0x7AEF,   0xB6CB,
          0x7AF9,   0xD6F1,
          0x7AFA,   0xF3C3,
          0x7AFD,   0xF3C4,
          0x7AFF,   0xB8CD,
          0x7B03,   0xF3C6,
          0x7B04,   0xF3C7,
          0x7B06,   0xB0CA,
          0x7B08,   0xF3C5,
          0x7B0A,   0xF3C9,
          0x7B0B,   0xCBF1,
          0x7B0F,   0xF3CB,
          0x7B11,   0xD0A6,
          0x7B14,   0xB1CA,
          0x7B15,   0xF3C8,
          0x7B19,   0xF3CF,
          0x7B1B,   0xB5D1,
          0x7B1E,   0xF3D7,
          0x7B20,   0xF3D2,
          0x7B24,   0xF3D4,
          0x7B25,   0xF3D3,
          0x7B26,   0xB7FB,
          0x7B28,   0xB1BF,
          0x7B2A,   0xF3CE,
          0x7B2B,   0xF3CA,
          0x7B2C,   0xB5DA,
          0x7B2E,   0xF3D0,
          0x7B31,   0xF3D1,
          0x7B33,   0xF3D5,
          0x7B38,   0xF3CD,
          0x7B3A,   0xBCE3,
          0x7B3C,   0xC1FD,
          0x7B3E,   0xF3D6,
          0x7B45,   0xF3DA,
          0x7B47,   0xF3CC,
          0x7B49,   0xB5C8,
          0x7B4B,   0xBDEE,
          0x7B4C,   0xF3DC,
          0x7B4F,   0xB7A4,
          0x7B50,   0xBFF0,
          0x7B51,   0xD6FE,
          0x7B52,   0xCDB2,
          0x7B54,   0xB4F0,
          0x7B56,   0xB2DF,
          0x7B58,   0xF3D8,
          0x7B5A,   0xF3D9,
          0x7B5B,   0xC9B8,
          0x7B5D,   0xF3DD,
          0x7B60,   0xF3DE,
          0x7B62,   0xF3E1,
          0x7B6E,   0xF3DF,
          0x7B71,   0xF3E3,
          0x7B72,   0xF3E2,
          0x7B75,   0xF3DB,
          0x7B77,   0xBFEA,
          0x7B79,   0xB3EF,
          0x7B7B,   0xF3E0,
          0x7B7E,   0xC7A9,
          0x7B80,   0xBCF2,
          0x7B85,   0xF3EB,
          0x7B8D,   0xB9BF,
          0x7B90,   0xF3E4,
          0x7B94,   0xB2AD,
          0x7B95,   0xBBFE,
          0x7B97,   0xCBE3,
          0x7B9C,   0xF3ED,
          0x7B9D,   0xF3E9,
          0x7BA1,   0xB9DC,
          0x7BA2,   0xF3EE,
          0x7BA6,   0xF3E5,
          0x7BA7,   0xF3E6,
          0x7BA8,   0xF3EA,
          0x7BA9,   0xC2E1,
          0x7BAA,   0xF3EC,
          0x7BAB,   0xF3EF,
          0x7BAC,   0xF3E8,
          0x7BAD,   0xBCFD,
          0x7BB1,   0xCFE4,
          0x7BB4,   0xF3F0,
          0x7BB8,   0xF3E7,
          0x7BC1,   0xF3F2,
          0x7BC6,   0xD7AD,
          0x7BC7,   0xC6AA,
          0x7BCC,   0xF3F3,
          0x7BD1,   0xF3F1,
          0x7BD3,   0xC2A8,
          0x7BD9,   0xB8DD,
          0x7BDA,   0xF3F5,
          0x7BDD,   0xF3F4,
          0x7BE1,   0xB4DB,
          0x7BE5,   0xF3F6,
          0x7BE6,   0xF3F7,
          0x7BEA,   0xF3F8,
          0x7BEE,   0xC0BA,
          0x7BF1,   0xC0E9,
          0x7BF7,   0xC5F1,
          0x7BFC,   0xF3FB,
          0x7BFE,   0xF3FA,
          0x7C07,   0xB4D8,
          0x7C0B,   0xF3FE,
          0x7C0C,   0xF3F9,
          0x7C0F,   0xF3FC,
          0x7C16,   0xF3FD,
          0x7C1F,   0xF4A1,
          0x7C26,   0xF4A3,
          0x7C27,   0xBBC9,
          0x7C2A,   0xF4A2,
          0x7C38,   0xF4A4,
          0x7C3F,   0xB2BE,
          0x7C40,   0xF4A6,
          0x7C41,   0xF4A5,
          0x7C4D,   0xBCAE,
          0x7C73,   0xC3D7,
          0x7C74,   0xD9E1,
          0x7C7B,   0xC0E0,
          0x7C7C,   0xF4CC,
          0x7C7D,   0xD7D1,
          0x7C89,   0xB7DB,
          0x7C91,   0xF4CE,
          0x7C92,   0xC1A3,
          0x7C95,   0xC6C9,
          0x7C97,   0xB4D6,
          0x7C98,   0xD5B3,
          0x7C9C,   0xF4D0,
          0x7C9D,   0xF4CF,
          0x7C9E,   0xF4D1,
          0x7C9F,   0xCBDA,
          0x7CA2,   0xF4D2,
          0x7CA4,   0xD4C1,
          0x7CA5,   0xD6E0,
          0x7CAA,   0xB7E0,
          0x7CAE,   0xC1B8,
          0x7CB1,   0xC1BB,
          0x7CB2,   0xF4D3,
          0x7CB3,   0xBEAC,
          0x7CB9,   0xB4E2,
          0x7CBC,   0xF4D4,
          0x7CBD,   0xF4D5,
          0x7CBE,   0xBEAB,
          0x7CC1,   0xF4D6,
          0x7CC5,   0xF4DB,
          0x7CC7,   0xF4D7,
          0x7CC8,   0xF4DA,
          0x7CCA,   0xBAFD,
          0x7CCC,   0xF4D8,
          0x7CCD,   0xF4D9,
          0x7CD5,   0xB8E2,
          0x7CD6,   0xCCC7,
          0x7CD7,   0xF4DC,
          0x7CD9,   0xB2DA,
          0x7CDC,   0xC3D3,
          0x7CDF,   0xD4E3,
          0x7CE0,   0xBFB7,
          0x7CE8,   0xF4DD,
          0x7CEF,   0xC5B4,
          0x7CF8,   0xF4E9,
          0x7CFB,   0xCFB5,
          0x7D0A,   0xCEC9,
          0x7D20,   0xCBD8,
          0x7D22,   0xCBF7,
          0x7D27,   0xBDF4,
          0x7D2B,   0xD7CF,
          0x7D2F,   0xC0DB,
          0x7D6E,   0xD0F5,
          0x7D77,   0xF4EA,
          0x7DA6,   0xF4EB,
          0x7DAE,   0xF4EC,
          0x7E3B,   0xF7E3,
          0x7E41,   0xB7B1,
          0x7E47,   0xF4ED,
          0x7E82,   0xD7EB,
          0x7E9B,   0xF4EE,
          0x7E9F,   0xE6F9,
          0x7EA0,   0xBEC0,
          0x7EA1,   0xE6FA,
          0x7EA2,   0xBAEC,
          0x7EA3,   0xE6FB,
          0x7EA4,   0xCFCB,
          0x7EA5,   0xE6FC,
          0x7EA6,   0xD4BC,
          0x7EA7,   0xBCB6,
          0x7EA8,   0xE6FD,
          0x7EA9,   0xE6FE,
          0x7EAA,   0xBCCD,
          0x7EAB,   0xC8D2,
          0x7EAC,   0xCEB3,
          0x7EAD,   0xE7A1,
          0x7EAF,   0xB4BF,
          0x7EB0,   0xE7A2,
          0x7EB1,   0xC9B4,
          0x7EB2,   0xB8D9,
          0x7EB3,   0xC4C9,
          0x7EB5,   0xD7DD,
          0x7EB6,   0xC2DA,
          0x7EB7,   0xB7D7,
          0x7EB8,   0xD6BD,
          0x7EB9,   0xCEC6,
          0x7EBA,   0xB7C4,
          0x7EBD,   0xC5A6,
          0x7EBE,   0xE7A3,
          0x7EBF,   0xCFDF,
          0x7EC0,   0xE7A4,
          0x7EC1,   0xE7A5,
          0x7EC2,   0xE7A6,
          0x7EC3,   0xC1B7,
          0x7EC4,   0xD7E9,
          0x7EC5,   0xC9F0,
          0x7EC6,   0xCFB8,
          0x7EC7,   0xD6AF,
          0x7EC8,   0xD6D5,
          0x7EC9,   0xE7A7,
          0x7ECA,   0xB0ED,
          0x7ECB,   0xE7A8,
          0x7ECC,   0xE7A9,
          0x7ECD,   0xC9DC,
          0x7ECE,   0xD2EF,
          0x7ECF,   0xBEAD,
          0x7ED0,   0xE7AA,
          0x7ED1,   0xB0F3,
          0x7ED2,   0xC8DE,
          0x7ED3,   0xBDE1,
          0x7ED4,   0xE7AB,
          0x7ED5,   0xC8C6,
          0x7ED7,   0xE7AC,
          0x7ED8,   0xBBE6,
          0x7ED9,   0xB8F8,
          0x7EDA,   0xD1A4,
          0x7EDB,   0xE7AD,
          0x7EDC,   0xC2E7,
          0x7EDD,   0xBEF8,
          0x7EDE,   0xBDCA,
          0x7EDF,   0xCDB3,
          0x7EE0,   0xE7AE,
          0x7EE1,   0xE7AF,
          0x7EE2,   0xBEEE,
          0x7EE3,   0xD0E5,
          0x7EE5,   0xCBE7,
          0x7EE6,   0xCCD0,
          0x7EE7,   0xBCCC,
          0x7EE8,   0xE7B0,
          0x7EE9,   0xBCA8,
          0x7EEA,   0xD0F7,
          0x7EEB,   0xE7B1,
          0x7EED,   0xD0F8,
          0x7EEE,   0xE7B2,
          0x7EEF,   0xE7B3,
          0x7EF0,   0xB4C2,
          0x7EF1,   0xE7B4,
          0x7EF2,   0xE7B5,
          0x7EF3,   0xC9FE,
          0x7EF4,   0xCEAC,
          0x7EF5,   0xC3E0,
          0x7EF6,   0xE7B7,
          0x7EF7,   0xB1C1,
          0x7EF8,   0xB3F1,
          0x7EFA,   0xE7B8,
          0x7EFB,   0xE7B9,
          0x7EFC,   0xD7DB,
          0x7EFD,   0xD5C0,
          0x7EFE,   0xE7BA,
          0x7EFF,   0xC2CC,
          0x7F00,   0xD7BA,
          0x7F01,   0xE7BB,
          0x7F02,   0xE7BC,
          0x7F03,   0xE7BD,
          0x7F04,   0xBCEA,
          0x7F05,   0xC3E5,
          0x7F06,   0xC0C2,
          0x7F07,   0xE7BE,
          0x7F08,   0xE7BF,
          0x7F09,   0xBCA9,
          0x7F0B,   0xE7C0,
          0x7F0C,   0xE7C1,
          0x7F0D,   0xE7B6,
          0x7F0E,   0xB6D0,
          0x7F0F,   0xE7C2,
          0x7F11,   0xE7C3,
          0x7F12,   0xE7C4,
          0x7F13,   0xBBBA,
          0x7F14,   0xB5DE,
          0x7F15,   0xC2C6,
          0x7F16,   0xB1E0,
          0x7F17,   0xE7C5,
          0x7F18,   0xD4B5,
          0x7F19,   0xE7C6,
          0x7F1A,   0xB8BF,
          0x7F1B,   0xE7C8,
          0x7F1C,   0xE7C7,
          0x7F1D,   0xB7EC,
          0x7F1F,   0xE7C9,
          0x7F20,   0xB2F8,
          0x7F21,   0xE7CA,
          0x7F22,   0xE7CB,
          0x7F23,   0xE7CC,
          0x7F24,   0xE7CD,
          0x7F25,   0xE7CE,
          0x7F26,   0xE7CF,
          0x7F27,   0xE7D0,
          0x7F28,   0xD3A7,
          0x7F29,   0xCBF5,
          0x7F2A,   0xE7D1,
          0x7F2B,   0xE7D2,
          0x7F2C,   0xE7D3,
          0x7F2D,   0xE7D4,
          0x7F2E,   0xC9C9,
          0x7F2F,   0xE7D5,
          0x7F30,   0xE7D6,
          0x7F31,   0xE7D7,
          0x7F32,   0xE7D8,
          0x7F33,   0xE7D9,
          0x7F34,   0xBDC9,
          0x7F35,   0xE7DA,
          0x7F36,   0xF3BE,
          0x7F38,   0xB8D7,
          0x7F3A,   0xC8B1,
          0x7F42,   0xF3BF,
          0x7F44,   0xF3C0,
          0x7F45,   0xF3C1,
          0x7F50,   0xB9DE,
          0x7F51,   0xCDF8,
          0x7F54,   0xD8E8,
          0x7F55,   0xBAB1,
          0x7F57,   0xC2DE,
          0x7F58,   0xEEB7,
          0x7F5A,   0xB7A3,
          0x7F5F,   0xEEB9,
          0x7F61,   0xEEB8,
          0x7F62,   0xB0D5,
          0x7F68,   0xEEBB,
          0x7F69,   0xD5D6,
          0x7F6A,   0xD7EF,
          0x7F6E,   0xD6C3,
          0x7F71,   0xEEBD,
          0x7F72,   0xCAF0,
          0x7F74,   0xEEBC,
          0x7F79,   0xEEBE,
          0x7F7E,   0xEEC0,
          0x7F81,   0xEEBF,
          0x7F8A,   0xD1F2,
          0x7F8C,   0xC7BC,
          0x7F8E,   0xC3C0,
          0x7F94,   0xB8E1,
          0x7F9A,   0xC1E7,
          0x7F9D,   0xF4C6,
          0x7F9E,   0xD0DF,
          0x7F9F,   0xF4C7,
          0x7FA1,   0xCFDB,
          0x7FA4,   0xC8BA,
          0x7FA7,   0xF4C8,
          0x7FAF,   0xF4C9,
          0x7FB0,   0xF4CA,
          0x7FB2,   0xF4CB,
          0x7FB8,   0xD9FA,
          0x7FB9,   0xB8FE,
          0x7FBC,   0xE5F1,
          0x7FBD,   0xD3F0,
          0x7FBF,   0xF4E0,
          0x7FC1,   0xCECC,
          0x7FC5,   0xB3E1,
          0x7FCA,   0xF1B4,
          0x7FCC,   0xD2EE,
          0x7FCE,   0xF4E1,
          0x7FD4,   0xCFE8,
          0x7FD5,   0xF4E2,
          0x7FD8,   0xC7CC,
          0x7FDF,   0xB5D4,
          0x7FE0,   0xB4E4,
          0x7FE1,   0xF4E4,
          0x7FE5,   0xF4E3,
          0x7FE6,   0xF4E5,
          0x7FE9,   0xF4E6,
          0x7FEE,   0xF4E7,
          0x7FF0,   0xBAB2,
          0x7FF1,   0xB0BF,
          0x7FF3,   0xF4E8,
          0x7FFB,   0xB7AD,
          0x7FFC,   0xD2ED,
          0x8000,   0xD2AB,
          0x8001,   0xC0CF,
          0x8003,   0xBFBC,
          0x8004,   0xEBA3,
          0x8005,   0xD5DF,
          0x8006,   0xEAC8,
          0x800B,   0xF1F3,
          0x800C,   0xB6F8,
          0x800D,   0xCBA3,
          0x8010,   0xC4CD,
          0x8012,   0xF1E7,
          0x8014,   0xF1E8,
          0x8015,   0xB8FB,
          0x8016,   0xF1E9,
          0x8017,   0xBAC4,
          0x8018,   0xD4C5,
          0x8019,   0xB0D2,
          0x801C,   0xF1EA,
          0x8020,   0xF1EB,
          0x8022,   0xF1EC,
          0x8025,   0xF1ED,
          0x8026,   0xF1EE,
          0x8027,   0xF1EF,
          0x8028,   0xF1F1,
          0x8029,   0xF1F0,
          0x802A,   0xC5D5,
          0x8031,   0xF1F2,
          0x8033,   0xB6FA,
          0x8035,   0xF1F4,
          0x8036,   0xD2AE,
          0x8037,   0xDEC7,
          0x8038,   0xCBCA,
          0x803B,   0xB3DC,
          0x803D,   0xB5A2,
          0x803F,   0xB9A2,
          0x8042,   0xC4F4,
          0x8043,   0xF1F5,
          0x8046,   0xF1F6,
          0x804A,   0xC1C4,
          0x804B,   0xC1FB,
          0x804C,   0xD6B0,
          0x804D,   0xF1F7,
          0x8052,   0xF1F8,
          0x8054,   0xC1AA,
          0x8058,   0xC6B8,
          0x805A,   0xBEDB,
          0x8069,   0xF1F9,
          0x806A,   0xB4CF,
          0x8071,   0xF1FA,
          0x807F,   0xEDB2,
          0x8080,   0xEDB1,
          0x8083,   0xCBE0,
          0x8084,   0xD2DE,
          0x8086,   0xCBC1,
          0x8087,   0xD5D8,
          0x8089,   0xC8E2,
          0x808B,   0xC0DF,
          0x808C,   0xBCA1,
          0x8093,   0xEBC1,
          0x8096,   0xD0A4,
          0x8098,   0xD6E2,
          0x809A,   0xB6C7,
          0x809B,   0xB8D8,
          0x809C,   0xEBC0,
          0x809D,   0xB8CE,
          0x809F,   0xEBBF,
          0x80A0,   0xB3A6,
          0x80A1,   0xB9C9,
          0x80A2,   0xD6AB,
          0x80A4,   0xB7F4,
          0x80A5,   0xB7CA,
          0x80A9,   0xBCE7,
          0x80AA,   0xB7BE,
          0x80AB,   0xEBC6,
          0x80AD,   0xEBC7,
          0x80AE,   0xB0B9,
          0x80AF,   0xBFCF,
          0x80B1,   0xEBC5,
          0x80B2,   0xD3FD,
          0x80B4,   0xEBC8,
          0x80B7,   0xEBC9,
          0x80BA,   0xB7CE,
          0x80BC,   0xEBC2,
          0x80BD,   0xEBC4,
          0x80BE,   0xC9F6,
          0x80BF,   0xD6D7,
          0x80C0,   0xD5CD,
          0x80C1,   0xD0B2,
          0x80C2,   0xEBCF,
          0x80C3,   0xCEB8,
          0x80C4,   0xEBD0,
          0x80C6,   0xB5A8,
          0x80CC,   0xB1B3,
          0x80CD,   0xEBD2,
          0x80CE,   0xCCA5,
          0x80D6,   0xC5D6,
          0x80D7,   0xEBD3,
          0x80D9,   0xEBD1,
          0x80DA,   0xC5DF,
          0x80DB,   0xEBCE,
          0x80DC,   0xCAA4,
          0x80DD,   0xEBD5,
          0x80DE,   0xB0FB,
          0x80E1,   0xBAFA,
          0x80E4,   0xD8B7,
          0x80E5,   0xF1E3,
          0x80E7,   0xEBCA,
          0x80E8,   0xEBCB,
          0x80E9,   0xEBCC,
          0x80EA,   0xEBCD,
          0x80EB,   0xEBD6,
          0x80EC,   0xE6C0,
          0x80ED,   0xEBD9,
          0x80EF,   0xBFE8,
          0x80F0,   0xD2C8,
          0x80F1,   0xEBD7,
          0x80F2,   0xEBDC,
          0x80F3,   0xB8EC,
          0x80F4,   0xEBD8,
          0x80F6,   0xBDBA,
          0x80F8,   0xD0D8,
          0x80FA,   0xB0B7,
          0x80FC,   0xEBDD,
          0x80FD,   0xC4DC,
          0x8102,   0xD6AC,
          0x8106,   0xB4E0,
          0x8109,   0xC2F6,
          0x810A,   0xBCB9,
          0x810D,   0xEBDA,
          0x810E,   0xEBDB,
          0x810F,   0xD4E0,
          0x8110,   0xC6EA,
          0x8111,   0xC4D4,
          0x8112,   0xEBDF,
          0x8113,   0xC5A7,
          0x8114,   0xD9F5,
          0x8116,   0xB2B1,
          0x8118,   0xEBE4,
          0x811A,   0xBDC5,
          0x811E,   0xEBE2,
          0x812C,   0xEBE3,
          0x812F,   0xB8AC,
          0x8131,   0xCDD1,
          0x8132,   0xEBE5,
          0x8136,   0xEBE1,
          0x8138,   0xC1B3,
          0x813E,   0xC6A2,
          0x8146,   0xCCF3,
          0x8148,   0xEBE6,
          0x814A,   0xC0B0,
          0x814B,   0xD2B8,
          0x814C,   0xEBE7,
          0x8150,   0xB8AF,
          0x8151,   0xB8AD,
          0x8153,   0xEBE8,
          0x8154,   0xC7BB,
          0x8155,   0xCDF3,
          0x8159,   0xEBEA,
          0x815A,   0xEBEB,
          0x8160,   0xEBED,
          0x8165,   0xD0C8,
          0x8167,   0xEBF2,
          0x8169,   0xEBEE,
          0x816D,   0xEBF1,
          0x816E,   0xC8F9,
          0x8170,   0xD1FC,
          0x8171,   0xEBEC,
          0x8174,   0xEBE9,
          0x8179,   0xB8B9,
          0x817A,   0xCFD9,
          0x817B,   0xC4E5,
          0x817C,   0xEBEF,
          0x817D,   0xEBF0,
          0x817E,   0xCCDA,
          0x817F,   0xCDC8,
          0x8180,   0xB0F2,
          0x8182,   0xEBF6,
          0x8188,   0xEBF5,
          0x818A,   0xB2B2,
          0x818F,   0xB8E0,
          0x8191,   0xEBF7,
          0x8198,   0xB1EC,
          0x819B,   0xCCC5,
          0x819C,   0xC4A4,
          0x819D,   0xCFA5,
          0x81A3,   0xEBF9,
          0x81A6,   0xECA2,
          0x81A8,   0xC5F2,
          0x81AA,   0xEBFA,
          0x81B3,   0xC9C5,
          0x81BA,   0xE2DF,
          0x81BB,   0xEBFE,
          0x81C0,   0xCDCE,
          0x81C1,   0xECA1,
          0x81C2,   0xB1DB,
          0x81C3,   0xD3B7,
          0x81C6,   0xD2DC,
          0x81CA,   0xEBFD,
          0x81CC,   0xEBFB,
          0x81E3,   0xB3BC,
          0x81E7,   0xEAB0,
          0x81EA,   0xD7D4,
          0x81EC,   0xF4AB,
          0x81ED,   0xB3F4,
          0x81F3,   0xD6C1,
          0x81F4,   0xD6C2,
          0x81FB,   0xD5E9,
          0x81FC,   0xBECA,
          0x81FE,   0xF4A7,
          0x8200,   0xD2A8,
          0x8201,   0xF4A8,
          0x8202,   0xF4A9,
          0x8204,   0xF4AA,
          0x8205,   0xBECB,
          0x8206,   0xD3DF,
          0x820C,   0xC9E0,
          0x820D,   0xC9E1,
          0x8210,   0xF3C2,
          0x8212,   0xCAE6,
          0x8214,   0xCCF2,
          0x821B,   0xE2B6,
          0x821C,   0xCBB4,
          0x821E,   0xCEE8,
          0x821F,   0xD6DB,
          0x8221,   0xF4AD,
          0x8222,   0xF4AE,
          0x8223,   0xF4AF,
          0x8228,   0xF4B2,
          0x822A,   0xBABD,
          0x822B,   0xF4B3,
          0x822C,   0xB0E3,
          0x822D,   0xF4B0,
          0x822F,   0xF4B1,
          0x8230,   0xBDA2,
          0x8231,   0xB2D5,
          0x8233,   0xF4B6,
          0x8234,   0xF4B7,
          0x8235,   0xB6E6,
          0x8236,   0xB2B0,
          0x8237,   0xCFCF,
          0x8238,   0xF4B4,
          0x8239,   0xB4AC,
          0x823B,   0xF4B5,
          0x823E,   0xF4B8,
          0x8244,   0xF4B9,
          0x8247,   0xCDA7,
          0x8249,   0xF4BA,
          0x824B,   0xF4BB,
          0x824F,   0xF4BC,
          0x8258,   0xCBD2,
          0x825A,   0xF4BD,
          0x825F,   0xF4BE,
          0x8268,   0xF4BF,
          0x826E,   0xF4DE,
          0x826F,   0xC1BC,
          0x8270,   0xBCE8,
          0x8272,   0xC9AB,
          0x8273,   0xD1DE,
          0x8274,   0xE5F5,
          0x8279,   0xDCB3,
          0x827A,   0xD2D5,
          0x827D,   0xDCB4,
          0x827E,   0xB0AC,
          0x827F,   0xDCB5,
          0x8282,   0xBDDA,
          0x8284,   0xDCB9,
          0x8288,   0xD8C2,
          0x828A,   0xDCB7,
          0x828B,   0xD3F3,
          0x828D,   0xC9D6,
          0x828E,   0xDCBA,
          0x828F,   0xDCB6,
          0x8291,   0xDCBB,
          0x8292,   0xC3A2,
          0x8297,   0xDCBC,
          0x8298,   0xDCC5,
          0x8299,   0xDCBD,
          0x829C,   0xCEDF,
          0x829D,   0xD6A5,
          0x829F,   0xDCCF,
          0x82A1,   0xDCCD,
          0x82A4,   0xDCD2,
          0x82A5,   0xBDE6,
          0x82A6,   0xC2AB,
          0x82A8,   0xDCB8,
          0x82A9,   0xDCCB,
          0x82AA,   0xDCCE,
          0x82AB,   0xDCBE,
          0x82AC,   0xB7D2,
          0x82AD,   0xB0C5,
          0x82AE,   0xDCC7,
          0x82AF,   0xD0BE,
          0x82B0,   0xDCC1,
          0x82B1,   0xBBA8,
          0x82B3,   0xB7BC,
          0x82B4,   0xDCCC,
          0x82B7,   0xDCC6,
          0x82B8,   0xDCBF,
          0x82B9,   0xC7DB,
          0x82BD,   0xD1BF,
          0x82BE,   0xDCC0,
          0x82C1,   0xDCCA,
          0x82C4,   0xDCD0,
          0x82C7,   0xCEAD,
          0x82C8,   0xDCC2,
          0x82CA,   0xDCC3,
          0x82CB,   0xDCC8,
          0x82CC,   0xDCC9,
          0x82CD,   0xB2D4,
          0x82CE,   0xDCD1,
          0x82CF,   0xCBD5,
          0x82D1,   0xD4B7,
          0x82D2,   0xDCDB,
          0x82D3,   0xDCDF,
          0x82D4,   0xCCA6,
          0x82D5,   0xDCE6,
          0x82D7,   0xC3E7,
          0x82D8,   0xDCDC,
          0x82DB,   0xBFC1,
          0x82DC,   0xDCD9,
          0x82DE,   0xB0FA,
          0x82DF,   0xB9B6,
          0x82E0,   0xDCE5,
          0x82E1,   0xDCD3,
          0x82E3,   0xDCC4,
          0x82E4,   0xDCD6,
          0x82E5,   0xC8F4,
          0x82E6,   0xBFE0,
          0x82EB,   0xC9BB,
          0x82EF,   0xB1BD,
          0x82F1,   0xD3A2,
          0x82F4,   0xDCDA,
          0x82F7,   0xDCD5,
          0x82F9,   0xC6BB,
          0x82FB,   0xDCDE,
          0x8301,   0xD7C2,
          0x8302,   0xC3AF,
          0x8303,   0xB7B6,
          0x8304,   0xC7D1,
          0x8305,   0xC3A9,
          0x8306,   0xDCE2,
          0x8307,   0xDCD8,
          0x8308,   0xDCEB,
          0x8309,   0xDCD4,
          0x830C,   0xDCDD,
          0x830E,   0xBEA5,
          0x830F,   0xDCD7,
          0x8311,   0xDCE0,
          0x8314,   0xDCE3,
          0x8315,   0xDCE4,
          0x8317,   0xDCF8,
          0x831A,   0xDCE1,
          0x831B,   0xDDA2,
          0x831C,   0xDCE7,
          0x8327,   0xBCEB,
          0x8328,   0xB4C4,
          0x832B,   0xC3A3,
          0x832C,   0xB2E7,
          0x832D,   0xDCFA,
          0x832F,   0xDCF2,
          0x8331,   0xDCEF,
          0x8333,   0xDCFC,
          0x8334,   0xDCEE,
          0x8335,   0xD2F0,
          0x8336,   0xB2E8,
          0x8338,   0xC8D7,
          0x8339,   0xC8E3,
          0x833A,   0xDCFB,
          0x833C,   0xDCED,
          0x8340,   0xDCF7,
          0x8343,   0xDCF5,
          0x8346,   0xBEA3,
          0x8347,   0xDCF4,
          0x8349,   0xB2DD,
          0x834F,   0xDCF3,
          0x8350,   0xBCF6,
          0x8351,   0xDCE8,
          0x8352,   0xBBC4,
          0x8354,   0xC0F3,
          0x835A,   0xBCD4,
          0x835B,   0xDCE9,
          0x835C,   0xDCEA,
          0x835E,   0xDCF1,
          0x835F,   0xDCF6,
          0x8360,   0xDCF9,
          0x8361,   0xB5B4,
          0x8363,   0xC8D9,
          0x8364,   0xBBE7,
          0x8365,   0xDCFE,
          0x8366,   0xDCFD,
          0x8367,   0xD3AB,
          0x8368,   0xDDA1,
          0x8369,   0xDDA3,
          0x836A,   0xDDA5,
          0x836B,   0xD2F1,
          0x836C,   0xDDA4,
          0x836D,   0xDDA6,
          0x836E,   0xDDA7,
          0x836F,   0xD2A9,
          0x8377,   0xBAC9,
          0x8378,   0xDDA9,
          0x837B,   0xDDB6,
          0x837C,   0xDDB1,
          0x837D,   0xDDB4,
          0x8385,   0xDDB0,
          0x8386,   0xC6CE,
          0x8389,   0xC0F2,
          0x838E,   0xC9AF,
          0x8392,   0xDCEC,
          0x8393,   0xDDAE,
          0x8398,   0xDDB7,
          0x839B,   0xDCF0,
          0x839C,   0xDDAF,
          0x839E,   0xDDB8,
          0x83A0,   0xDDAC,
          0x83A8,   0xDDB9,
          0x83A9,   0xDDB3,
          0x83AA,   0xDDAD,
          0x83AB,   0xC4AA,
          0x83B0,   0xDDA8,
          0x83B1,   0xC0B3,
          0x83B2,   0xC1AB,
          0x83B3,   0xDDAA,
          0x83B4,   0xDDAB,
          0x83B6,   0xDDB2,
          0x83B7,   0xBBF1,
          0x83B8,   0xDDB5,
          0x83B9,   0xD3A8,
          0x83BA,   0xDDBA,
          0x83BC,   0xDDBB,
          0x83BD,   0xC3A7,
          0x83C0,   0xDDD2,
          0x83C1,   0xDDBC,
          0x83C5,   0xDDD1,
          0x83C7,   0xB9BD,
          0x83CA,   0xBED5,
          0x83CC,   0xBEFA,
          0x83CF,   0xBACA,
          0x83D4,   0xDDCA,
          0x83D6,   0xDDC5,
          0x83D8,   0xDDBF,
          0x83DC,   0xB2CB,
          0x83DD,   0xDDC3,
          0x83DF,   0xDDCB,
          0x83E0,   0xB2A4,
          0x83E1,   0xDDD5,
          0x83E5,   0xDDBE,
          0x83E9,   0xC6D0,
          0x83EA,   0xDDD0,
          0x83F0,   0xDDD4,
          0x83F1,   0xC1E2,
          0x83F2,   0xB7C6,
          0x83F8,   0xDDCE,
          0x83F9,   0xDDCF,
          0x83FD,   0xDDC4,
          0x8401,   0xDDBD,
          0x8403,   0xDDCD,
          0x8404,   0xCCD1,
          0x8406,   0xDDC9,
          0x840B,   0xDDC2,
          0x840C,   0xC3C8,
          0x840D,   0xC6BC,
          0x840E,   0xCEAE,
          0x840F,   0xDDCC,
          0x8411,   0xDDC8,
          0x8418,   0xDDC1,
          0x841C,   0xDDC6,
          0x841D,   0xC2DC,
          0x8424,   0xD3A9,
          0x8425,   0xD3AA,
          0x8426,   0xDDD3,
          0x8427,   0xCFF4,
          0x8428,   0xC8F8,
          0x8431,   0xDDE6,
          0x8438,   0xDDC7,
          0x843C,   0xDDE0,
          0x843D,   0xC2E4,
          0x8446,   0xDDE1,
          0x8451,   0xDDD7,
          0x8457,   0xD6F8,
          0x8459,   0xDDD9,
          0x845A,   0xDDD8,
          0x845B,   0xB8F0,
          0x845C,   0xDDD6,
          0x8461,   0xC6CF,
          0x8463,   0xB6AD,
          0x8469,   0xDDE2,
          0x846B,   0xBAF9,
          0x846C,   0xD4E1,
          0x846D,   0xDDE7,
          0x8471,   0xB4D0,
          0x8473,   0xDDDA,
          0x8475,   0xBFFB,
          0x8476,   0xDDE3,
          0x8478,   0xDDDF,
          0x847A,   0xDDDD,
          0x8482,   0xB5D9,
          0x8487,   0xDDDB,
          0x8488,   0xDDDC,
          0x8489,   0xDDDE,
          0x848B,   0xBDAF,
          0x848C,   0xDDE4,
          0x848E,   0xDDE5,
          0x8497,   0xDDF5,
          0x8499,   0xC3C9,
          0x849C,   0xCBE2,
          0x84A1,   0xDDF2,
          0x84AF,   0xD8E1,
          0x84B2,   0xC6D1,
          0x84B4,   0xDDF4,
          0x84B8,   0xD5F4,
          0x84B9,   0xDDF3,
          0x84BA,   0xDDF0,
          0x84BD,   0xDDEC,
          0x84BF,   0xDDEF,
          0x84C1,   0xDDE8,
          0x84C4,   0xD0EE,
          0x84C9,   0xC8D8,
          0x84CA,   0xDDEE,
          0x84CD,   0xDDE9,
          0x84D0,   0xDDEA,
          0x84D1,   0xCBF2,
          0x84D3,   0xDDED,
          0x84D6,   0xB1CD,
          0x84DD,   0xC0B6,
          0x84DF,   0xBCBB,
          0x84E0,   0xDDF1,
          0x84E3,   0xDDF7,
          0x84E5,   0xDDF6,
          0x84E6,   0xDDEB,
          0x84EC,   0xC5EE,
          0x84F0,   0xDDFB,
          0x84FC,   0xDEA4,
          0x84FF,   0xDEA3,
          0x850C,   0xDDF8,
          0x8511,   0xC3EF,
          0x8513,   0xC2FB,
          0x8517,   0xD5E1,
          0x851A,   0xCEB5,
          0x851F,   0xDDFD,
          0x8521,   0xB2CC,
          0x852B,   0xC4E8,
          0x852C,   0xCADF,
          0x8537,   0xC7BE,
          0x8538,   0xDDFA,
          0x8539,   0xDDFC,
          0x853A,   0xDDFE,
          0x853B,   0xDEA2,
          0x853C,   0xB0AA,
          0x853D,   0xB1CE,
          0x8543,   0xDEAC,
          0x8548,   0xDEA6,
          0x8549,   0xBDB6,
          0x854A,   0xC8EF,
          0x8556,   0xDEA1,
          0x8559,   0xDEA5,
          0x855E,   0xDEA9,
          0x8564,   0xDEA8,
          0x8568,   0xDEA7,
          0x8572,   0xDEAD,
          0x8574,   0xD4CC,
          0x8579,   0xDEB3,
          0x857A,   0xDEAA,
          0x857B,   0xDEAE,
          0x857E,   0xC0D9,
          0x8584,   0xB1A1,
          0x8585,   0xDEB6,
          0x8587,   0xDEB1,
          0x858F,   0xDEB2,
          0x859B,   0xD1A6,
          0x859C,   0xDEB5,
          0x85A4,   0xDEAF,
          0x85A8,   0xDEB0,
          0x85AA,   0xD0BD,
          0x85AE,   0xDEB4,
          0x85AF,   0xCAED,
          0x85B0,   0xDEB9,
          0x85B7,   0xDEB8,
          0x85B9,   0xDEB7,
          0x85C1,   0xDEBB,
          0x85C9,   0xBDE5,
          0x85CF,   0xB2D8,
          0x85D0,   0xC3EA,
          0x85D3,   0xDEBA,
          0x85D5,   0xC5BA,
          0x85DC,   0xDEBC,
          0x85E4,   0xCCD9,
          0x85E9,   0xB7AA,
          0x85FB,   0xD4E5,
          0x85FF,   0xDEBD,
          0x8605,   0xDEBF,
          0x8611,   0xC4A2,
          0x8616,   0xDEC1,
          0x8627,   0xDEBE,
          0x8629,   0xDEC0,
          0x8638,   0xD5BA,
          0x863C,   0xDEC2,
          0x864D,   0xF2AE,
          0x864E,   0xBBA2,
          0x864F,   0xC2B2,
          0x8650,   0xC5B0,
          0x8651,   0xC2C7,
          0x8654,   0xF2AF,
          0x865A,   0xD0E9,
          0x865E,   0xD3DD,
          0x8662,   0xEBBD,
          0x866B,   0xB3E6,
          0x866C,   0xF2B0,
          0x866E,   0xF2B1,
          0x8671,   0xCAAD,
          0x8679,   0xBAE7,
          0x867A,   0xF2B3,
          0x867B,   0xF2B5,
          0x867C,   0xF2B4,
          0x867D,   0xCBE4,
          0x867E,   0xCFBA,
          0x867F,   0xF2B2,
          0x8680,   0xCAB4,
          0x8681,   0xD2CF,
          0x8682,   0xC2EC,
          0x868A,   0xCEC3,
          0x868B,   0xF2B8,
          0x868C,   0xB0F6,
          0x868D,   0xF2B7,
          0x8693,   0xF2BE,
          0x8695,   0xB2CF,
          0x869C,   0xD1C1,
          0x869D,   0xF2BA,
          0x86A3,   0xF2BC,
          0x86A4,   0xD4E9,
          0x86A7,   0xF2BB,
          0x86A8,   0xF2B6,
          0x86A9,   0xF2BF,
          0x86AA,   0xF2BD,
          0x86AC,   0xF2B9,
          0x86AF,   0xF2C7,
          0x86B0,   0xF2C4,
          0x86B1,   0xF2C6,
          0x86B4,   0xF2CA,
          0x86B5,   0xF2C2,
          0x86B6,   0xF2C0,
          0x86BA,   0xF2C5,
          0x86C0,   0xD6FB,
          0x86C4,   0xF2C1,
          0x86C6,   0xC7F9,
          0x86C7,   0xC9DF,
          0x86C9,   0xF2C8,
          0x86CA,   0xB9C6,
          0x86CB,   0xB5B0,
          0x86CE,   0xF2C3,
          0x86CF,   0xF2C9,
          0x86D0,   0xF2D0,
          0x86D1,   0xF2D6,
          0x86D4,   0xBBD7,
          0x86D8,   0xF2D5,
          0x86D9,   0xCDDC,
          0x86DB,   0xD6EB,
          0x86DE,   0xF2D2,
          0x86DF,   0xF2D4,
          0x86E4,   0xB8F2,
          0x86E9,   0xF2CB,
          0x86ED,   0xF2CE,
          0x86EE,   0xC2F9,
          0x86F0,   0xD5DD,
          0x86F1,   0xF2CC,
          0x86F2,   0xF2CD,
          0x86F3,   0xF2CF,
          0x86F4,   0xF2D3,
          0x86F8,   0xF2D9,
          0x86F9,   0xD3BC,
          0x86FE,   0xB6EA,
          0x8700,   0xCAF1,
          0x8702,   0xB7E4,
          0x8703,   0xF2D7,
          0x8707,   0xF2D8,
          0x8708,   0xF2DA,
          0x8709,   0xF2DD,
          0x870A,   0xF2DB,
          0x870D,   0xF2DC,
          0x8712,   0xD1D1,
          0x8713,   0xF2D1,
          0x8715,   0xCDC9,
          0x8717,   0xCECF,
          0x8718,   0xD6A9,
          0x871A,   0xF2E3,
          0x871C,   0xC3DB,
          0x871E,   0xF2E0,
          0x8721,   0xC0AF,
          0x8722,   0xF2EC,
          0x8723,   0xF2DE,
          0x8725,   0xF2E1,
          0x8729,   0xF2E8,
          0x872E,   0xF2E2,
          0x8731,   0xF2E7,
          0x8734,   0xF2E6,
          0x8737,   0xF2E9,
          0x873B,   0xF2DF,
          0x873E,   0xF2E4,
          0x873F,   0xF2EA,
          0x8747,   0xD3AC,
          0x8748,   0xF2E5,
          0x8749,   0xB2F5,
          0x874C,   0xF2F2,
          0x874E,   0xD0AB,
          0x8753,   0xF2F5,
          0x8757,   0xBBC8,
          0x8759,   0xF2F9,
          0x8760,   0xF2F0,
          0x8763,   0xF2F6,
          0x8764,   0xF2F8,
          0x8765,   0xF2FA,
          0x876E,   0xF2F3,
          0x8770,   0xF2F1,
          0x8774,   0xBAFB,
          0x8776,   0xB5FB,
          0x877B,   0xF2EF,
          0x877C,   0xF2F7,
          0x877D,   0xF2ED,
          0x877E,   0xF2EE,
          0x8782,   0xF2EB,
          0x8783,   0xF3A6,
          0x8785,   0xF3A3,
          0x8788,   0xF3A2,
          0x878B,   0xF2F4,
          0x878D,   0xC8DA,
          0x8793,   0xF2FB,
          0x8797,   0xF3A5,
          0x879F,   0xC3F8,
          0x87A8,   0xF2FD,
          0x87AB,   0xF3A7,
          0x87AC,   0xF3A9,
          0x87AD,   0xF3A4,
          0x87AF,   0xF2FC,
          0x87B3,   0xF3AB,
          0x87B5,   0xF3AA,
          0x87BA,   0xC2DD,
          0x87BD,   0xF3AE,
          0x87C0,   0xF3B0,
          0x87C6,   0xF3A1,
          0x87CA,   0xF3B1,
          0x87CB,   0xF3AC,
          0x87D1,   0xF3AF,
          0x87D2,   0xF2FE,
          0x87D3,   0xF3AD,
          0x87DB,   0xF3B2,
          0x87E0,   0xF3B4,
          0x87E5,   0xF3A8,
          0x87EA,   0xF3B3,
          0x87EE,   0xF3B5,
          0x87F9,   0xD0B7,
          0x87FE,   0xF3B8,
          0x8803,   0xD9F9,
          0x880A,   0xF3B9,
          0x8813,   0xF3B7,
          0x8815,   0xC8E4,
          0x8816,   0xF3B6,
          0x881B,   0xF3BA,
          0x8821,   0xF3BB,
          0x8822,   0xB4C0,
          0x8832,   0xEEC3,
          0x8839,   0xF3BC,
          0x883C,   0xF3BD,
          0x8840,   0xD1AA,
          0x8844,   0xF4AC,
          0x8845,   0xD0C6,
          0x884C,   0xD0D0,
          0x884D,   0xD1DC,
          0x8854,   0xCFCE,
          0x8857,   0xBDD6,
          0x8859,   0xD1C3,
          0x8861,   0xBAE2,
          0x8862,   0xE1E9,
          0x8863,   0xD2C2,
          0x8864,   0xF1C2,
          0x8865,   0xB2B9,
          0x8868,   0xB1ED,
          0x8869,   0xF1C3,
          0x886B,   0xC9C0,
          0x886C,   0xB3C4,
          0x886E,   0xD9F2,
          0x8870,   0xCBA5,
          0x8872,   0xF1C4,
          0x8877,   0xD6D4,
          0x887D,   0xF1C5,
          0x887E,   0xF4C0,
          0x887F,   0xF1C6,
          0x8881,   0xD4AC,
          0x8882,   0xF1C7,
          0x8884,   0xB0C0,
          0x8885,   0xF4C1,
          0x8888,   0xF4C2,
          0x888B,   0xB4FC,
          0x888D,   0xC5DB,
          0x8892,   0xCCBB,
          0x8896,   0xD0E4,
          0x889C,   0xCDE0,
          0x88A2,   0xF1C8,
          0x88A4,   0xD9F3,
          0x88AB,   0xB1BB,
          0x88AD,   0xCFAE,
          0x88B1,   0xB8A4,
          0x88B7,   0xF1CA,
          0x88BC,   0xF1CB,
          0x88C1,   0xB2C3,
          0x88C2,   0xC1D1,
          0x88C5,   0xD7B0,
          0x88C6,   0xF1C9,
          0x88C9,   0xF1CC,
          0x88CE,   0xF1CE,
          0x88D2,   0xD9F6,
          0x88D4,   0xD2E1,
          0x88D5,   0xD4A3,
          0x88D8,   0xF4C3,
          0x88D9,   0xC8B9,
          0x88DF,   0xF4C4,
          0x88E2,   0xF1CD,
          0x88E3,   0xF1CF,
          0x88E4,   0xBFE3,
          0x88E5,   0xF1D0,
          0x88E8,   0xF1D4,
          0x88F0,   0xF1D6,
          0x88F1,   0xF1D1,
          0x88F3,   0xC9D1,
          0x88F4,   0xC5E1,
          0x88F8,   0xC2E3,
          0x88F9,   0xB9FC,
          0x88FC,   0xF1D3,
          0x88FE,   0xF1D5,
          0x8902,   0xB9D3,
          0x890A,   0xF1DB,
          0x8910,   0xBAD6,
          0x8912,   0xB0FD,
          0x8913,   0xF1D9,
          0x8919,   0xF1D8,
          0x891A,   0xF1D2,
          0x891B,   0xF1DA,
          0x8921,   0xF1D7,
          0x8925,   0xC8EC,
          0x892A,   0xCDCA,
          0x892B,   0xF1DD,
          0x8930,   0xE5BD,
          0x8934,   0xF1DC,
          0x8936,   0xF1DE,
          0x8941,   0xF1DF,
          0x8944,   0xCFE5,
          0x895E,   0xF4C5,
          0x895F,   0xBDF3,
          0x8966,   0xF1E0,
          0x897B,   0xF1E1,
          0x897F,   0xCEF7,
          0x8981,   0xD2AA,
          0x8983,   0xF1FB,
          0x8986,   0xB8B2,
          0x89C1,   0xBCFB,
          0x89C2,   0xB9DB,
          0x89C4,   0xB9E6,
          0x89C5,   0xC3D9,
          0x89C6,   0xCAD3,
          0x89C7,   0xEAE8,
          0x89C8,   0xC0C0,
          0x89C9,   0xBEF5,
          0x89CA,   0xEAE9,
          0x89CB,   0xEAEA,
          0x89CC,   0xEAEB,
          0x89CE,   0xEAEC,
          0x89CF,   0xEAED,
          0x89D0,   0xEAEE,
          0x89D1,   0xEAEF,
          0x89D2,   0xBDC7,
          0x89D6,   0xF5FB,
          0x89DA,   0xF5FD,
          0x89DC,   0xF5FE,
          0x89DE,   0xF5FC,
          0x89E3,   0xBDE2,
          0x89E5,   0xF6A1,
          0x89E6,   0xB4A5,
          0x89EB,   0xF6A2,
          0x89EF,   0xF6A3,
          0x89F3,   0xECB2,
          0x8A00,   0xD1D4,
          0x8A07,   0xD9EA,
          0x8A3E,   0xF6A4,
          0x8A48,   0xEEBA,
          0x8A79,   0xD5B2,
          0x8A89,   0xD3FE,
          0x8A8A,   0xCCDC,
          0x8A93,   0xCAC4,
          0x8B07,   0xE5C0,
          0x8B26,   0xF6A5,
          0x8B66,   0xBEAF,
          0x8B6C,   0xC6A9,
          0x8BA0,   0xDAA5,
          0x8BA1,   0xBCC6,
          0x8BA2,   0xB6A9,
          0x8BA3,   0xB8BC,
          0x8BA4,   0xC8CF,
          0x8BA5,   0xBCA5,
          0x8BA6,   0xDAA6,
          0x8BA7,   0xDAA7,
          0x8BA8,   0xCCD6,
          0x8BA9,   0xC8C3,
          0x8BAA,   0xDAA8,
          0x8BAB,   0xC6FD,
          0x8BAD,   0xD1B5,
          0x8BAE,   0xD2E9,
          0x8BAF,   0xD1B6,
          0x8BB0,   0xBCC7,
          0x8BB2,   0xBDB2,
          0x8BB3,   0xBBE4,
          0x8BB4,   0xDAA9,
          0x8BB5,   0xDAAA,
          0x8BB6,   0xD1C8,
          0x8BB7,   0xDAAB,
          0x8BB8,   0xD0ED,
          0x8BB9,   0xB6EF,
          0x8BBA,   0xC2DB,
          0x8BBC,   0xCBCF,
          0x8BBD,   0xB7ED,
          0x8BBE,   0xC9E8,
          0x8BBF,   0xB7C3,
          0x8BC0,   0xBEF7,
          0x8BC1,   0xD6A4,
          0x8BC2,   0xDAAC,
          0x8BC3,   0xDAAD,
          0x8BC4,   0xC6C0,
          0x8BC5,   0xD7E7,
          0x8BC6,   0xCAB6,
          0x8BC8,   0xD5A9,
          0x8BC9,   0xCBDF,
          0x8BCA,   0xD5EF,
          0x8BCB,   0xDAAE,
          0x8BCC,   0xD6DF,
          0x8BCD,   0xB4CA,
          0x8BCE,   0xDAB0,
          0x8BCF,   0xDAAF,
          0x8BD1,   0xD2EB,
          0x8BD2,   0xDAB1,
          0x8BD3,   0xDAB2,
          0x8BD4,   0xDAB3,
          0x8BD5,   0xCAD4,
          0x8BD6,   0xDAB4,
          0x8BD7,   0xCAAB,
          0x8BD8,   0xDAB5,
          0x8BD9,   0xDAB6,
          0x8BDA,   0xB3CF,
          0x8BDB,   0xD6EF,
          0x8BDC,   0xDAB7,
          0x8BDD,   0xBBB0,
          0x8BDE,   0xB5AE,
          0x8BDF,   0xDAB8,
          0x8BE0,   0xDAB9,
          0x8BE1,   0xB9EE,
          0x8BE2,   0xD1AF,
          0x8BE3,   0xD2E8,
          0x8BE4,   0xDABA,
          0x8BE5,   0xB8C3,
          0x8BE6,   0xCFEA,
          0x8BE7,   0xB2EF,
          0x8BE8,   0xDABB,
          0x8BE9,   0xDABC,
          0x8BEB,   0xBDEB,
          0x8BEC,   0xCEDC,
          0x8BED,   0xD3EF,
          0x8BEE,   0xDABD,
          0x8BEF,   0xCEF3,
          0x8BF0,   0xDABE,
          0x8BF1,   0xD3D5,
          0x8BF2,   0xBBE5,
          0x8BF3,   0xDABF,
          0x8BF4,   0xCBB5,
          0x8BF5,   0xCBD0,
          0x8BF6,   0xDAC0,
          0x8BF7,   0xC7EB,
          0x8BF8,   0xD6EE,
          0x8BF9,   0xDAC1,
          0x8BFA,   0xC5B5,
          0x8BFB,   0xB6C1,
          0x8BFC,   0xDAC2,
          0x8BFD,   0xB7CC,
          0x8BFE,   0xBFCE,
          0x8BFF,   0xDAC3,
          0x8C00,   0xDAC4,
          0x8C01,   0xCBAD,
          0x8C02,   0xDAC5,
          0x8C03,   0xB5F7,
          0x8C04,   0xDAC6,
          0x8C05,   0xC1C2,
          0x8C06,   0xD7BB,
          0x8C07,   0xDAC7,
          0x8C08,   0xCCB8,
          0x8C0A,   0xD2EA,
          0x8C0B,   0xC4B1,
          0x8C0C,   0xDAC8,
          0x8C0D,   0xB5FD,
          0x8C0E,   0xBBD1,
          0x8C0F,   0xDAC9,
          0x8C10,   0xD0B3,
          0x8C11,   0xDACA,
          0x8C12,   0xDACB,
          0x8C13,   0xCEBD,
          0x8C14,   0xDACC,
          0x8C15,   0xDACD,
          0x8C16,   0xDACE,
          0x8C17,   0xB2F7,
          0x8C18,   0xDAD1,
          0x8C19,   0xDACF,
          0x8C1A,   0xD1E8,
          0x8C1B,   0xDAD0,
          0x8C1C,   0xC3D5,
          0x8C1D,   0xDAD2,
          0x8C1F,   0xDAD3,
          0x8C20,   0xDAD4,
          0x8C21,   0xDAD5,
          0x8C22,   0xD0BB,
          0x8C23,   0xD2A5,
          0x8C24,   0xB0F9,
          0x8C25,   0xDAD6,
          0x8C26,   0xC7AB,
          0x8C27,   0xDAD7,
          0x8C28,   0xBDF7,
          0x8C29,   0xC3A1,
          0x8C2A,   0xDAD8,
          0x8C2B,   0xDAD9,
          0x8C2C,   0xC3FD,
          0x8C2D,   0xCCB7,
          0x8C2E,   0xDADA,
          0x8C2F,   0xDADB,
          0x8C30,   0xC0BE,
          0x8C31,   0xC6D7,
          0x8C32,   0xDADC,
          0x8C33,   0xDADD,
          0x8C34,   0xC7B4,
          0x8C35,   0xDADE,
          0x8C36,   0xDADF,
          0x8C37,   0xB9C8,
          0x8C41,   0xBBED,
          0x8C46,   0xB6B9,
          0x8C47,   0xF4F8,
          0x8C49,   0xF4F9,
          0x8C4C,   0xCDE3,
          0x8C55,   0xF5B9,
          0x8C5A,   0xEBE0,
          0x8C61,   0xCFF3,
          0x8C62,   0xBBBF,
          0x8C6A,   0xBAC0,
          0x8C6B,   0xD4A5,
          0x8C73,   0xE1D9,
          0x8C78,   0xF5F4,
          0x8C79,   0xB1AA,
          0x8C7A,   0xB2F2,
          0x8C82,   0xF5F5,
          0x8C85,   0xF5F7,
          0x8C89,   0xBAD1,
          0x8C8A,   0xF5F6,
          0x8C8C,   0xC3B2,
          0x8C94,   0xF5F9,
          0x8C98,   0xF5F8,
          0x8D1D,   0xB1B4,
          0x8D1E,   0xD5EA,
          0x8D1F,   0xB8BA,
          0x8D21,   0xB9B1,
          0x8D22,   0xB2C6,
          0x8D23,   0xD4F0,
          0x8D24,   0xCFCD,
          0x8D25,   0xB0DC,
          0x8D26,   0xD5CB,
          0x8D27,   0xBBF5,
          0x8D28,   0xD6CA,
          0x8D29,   0xB7B7,
          0x8D2A,   0xCCB0,
          0x8D2B,   0xC6B6,
          0x8D2C,   0xB1E1,
          0x8D2D,   0xB9BA,
          0x8D2E,   0xD6FC,
          0x8D2F,   0xB9E1,
          0x8D30,   0xB7A1,
          0x8D31,   0xBCFA,
          0x8D32,   0xEADA,
          0x8D33,   0xEADB,
          0x8D34,   0xCCF9,
          0x8D35,   0xB9F3,
          0x8D36,   0xEADC,
          0x8D37,   0xB4FB,
          0x8D38,   0xC3B3,
          0x8D39,   0xB7D1,
          0x8D3A,   0xBAD8,
          0x8D3B,   0xEADD,
          0x8D3C,   0xD4F4,
          0x8D3D,   0xEADE,
          0x8D3E,   0xBCD6,
          0x8D3F,   0xBBDF,
          0x8D40,   0xEADF,
          0x8D41,   0xC1DE,
          0x8D42,   0xC2B8,
          0x8D43,   0xD4DF,
          0x8D44,   0xD7CA,
          0x8D45,   0xEAE0,
          0x8D46,   0xEAE1,
          0x8D47,   0xEAE4,
          0x8D48,   0xEAE2,
          0x8D49,   0xEAE3,
          0x8D4A,   0xC9DE,
          0x8D4B,   0xB8B3,
          0x8D4C,   0xB6C4,
          0x8D4D,   0xEAE5,
          0x8D4E,   0xCAEA,
          0x8D4F,   0xC9CD,
          0x8D50,   0xB4CD,
          0x8D53,   0xE2D9,
          0x8D54,   0xC5E2,
          0x8D55,   0xEAE6,
          0x8D56,   0xC0B5,
          0x8D58,   0xD7B8,
          0x8D59,   0xEAE7,
          0x8D5A,   0xD7AC,
          0x8D5B,   0xC8FC,
          0x8D5C,   0xD8D3,
          0x8D5D,   0xD8CD,
          0x8D5E,   0xD4DE,
          0x8D60,   0xD4F9,
          0x8D61,   0xC9C4,
          0x8D62,   0xD3AE,
          0x8D63,   0xB8D3,
          0x8D64,   0xB3E0,
          0x8D66,   0xC9E2,
          0x8D67,   0xF4F6,
          0x8D6B,   0xBAD5,
          0x8D6D,   0xF4F7,
          0x8D70,   0xD7DF,
          0x8D73,   0xF4F1,
          0x8D74,   0xB8B0,
          0x8D75,   0xD5D4,
          0x8D76,   0xB8CF,
          0x8D77,   0xC6F0,
          0x8D81,   0xB3C3,
          0x8D84,   0xF4F2,
          0x8D85,   0xB3AC,
          0x8D8A,   0xD4BD,
          0x8D8B,   0xC7F7,
          0x8D91,   0xF4F4,
          0x8D94,   0xF4F3,
          0x8D9F,   0xCCCB,
          0x8DA3,   0xC8A4,
          0x8DB1,   0xF4F5,
          0x8DB3,   0xD7E3,
          0x8DB4,   0xC5BF,
          0x8DB5,   0xF5C0,
          0x8DB8,   0xF5BB,
          0x8DBA,   0xF5C3,
          0x8DBC,   0xF5C2,
          0x8DBE,   0xD6BA,
          0x8DBF,   0xF5C1,
          0x8DC3,   0xD4BE,
          0x8DC4,   0xF5C4,
          0x8DC6,   0xF5CC,
          0x8DCB,   0xB0CF,
          0x8DCC,   0xB5F8,
          0x8DCE,   0xF5C9,
          0x8DCF,   0xF5CA,
          0x8DD1,   0xC5DC,
          0x8DD6,   0xF5C5,
          0x8DD7,   0xF5C6,
          0x8DDA,   0xF5C7,
          0x8DDB,   0xF5CB,
          0x8DDD,   0xBEE0,
          0x8DDE,   0xF5C8,
          0x8DDF,   0xB8FA,
          0x8DE3,   0xF5D0,
          0x8DE4,   0xF5D3,
          0x8DE8,   0xBFE7,
          0x8DEA,   0xB9F2,
          0x8DEB,   0xF5BC,
          0x8DEC,   0xF5CD,
          0x8DEF,   0xC2B7,
          0x8DF3,   0xCCF8,
          0x8DF5,   0xBCF9,
          0x8DF7,   0xF5CE,
          0x8DF8,   0xF5CF,
          0x8DF9,   0xF5D1,
          0x8DFA,   0xB6E5,
          0x8DFB,   0xF5D2,
          0x8DFD,   0xF5D5,
          0x8E05,   0xF5BD,
          0x8E09,   0xF5D4,
          0x8E0A,   0xD3BB,
          0x8E0C,   0xB3EC,
          0x8E0F,   0xCCA4,
          0x8E14,   0xF5D6,
          0x8E1D,   0xF5D7,
          0x8E1E,   0xBEE1,
          0x8E1F,   0xF5D8,
          0x8E22,   0xCCDF,
          0x8E23,   0xF5DB,
          0x8E29,   0xB2C8,
          0x8E2A,   0xD7D9,
          0x8E2C,   0xF5D9,
          0x8E2E,   0xF5DA,
          0x8E2F,   0xF5DC,
          0x8E31,   0xF5E2,
          0x8E35,   0xF5E0,
          0x8E39,   0xF5DF,
          0x8E3A,   0xF5DD,
          0x8E3D,   0xF5E1,
          0x8E40,   0xF5DE,
          0x8E41,   0xF5E4,
          0x8E42,   0xF5E5,
          0x8E44,   0xCCE3,
          0x8E47,   0xE5BF,
          0x8E48,   0xB5B8,
          0x8E49,   0xF5E3,
          0x8E4A,   0xF5E8,
          0x8E4B,   0xCCA3,
          0x8E51,   0xF5E6,
          0x8E52,   0xF5E7,
          0x8E59,   0xF5BE,
          0x8E66,   0xB1C4,
          0x8E69,   0xF5BF,
          0x8E6C,   0xB5C5,
          0x8E6D,   0xB2E4,
          0x8E6F,   0xF5EC,
          0x8E70,   0xF5E9,
          0x8E72,   0xB6D7,
          0x8E74,   0xF5ED,
          0x8E76,   0xF5EA,
          0x8E7C,   0xF5EB,
          0x8E7F,   0xB4DA,
          0x8E81,   0xD4EA,
          0x8E85,   0xF5EE,
          0x8E87,   0xB3F9,
          0x8E8F,   0xF5EF,
          0x8E90,   0xF5F1,
          0x8E94,   0xF5F0,
          0x8E9C,   0xF5F2,
          0x8E9E,   0xF5F3,
          0x8EAB,   0xC9ED,
          0x8EAC,   0xB9AA,
          0x8EAF,   0xC7FB,
          0x8EB2,   0xB6E3,
          0x8EBA,   0xCCC9,
          0x8ECE,   0xEAA6,
          0x8F66,   0xB3B5,
          0x8F67,   0xD4FE,
          0x8F68,   0xB9EC,
          0x8F69,   0xD0F9,
          0x8F6B,   0xE9ED,
          0x8F6C,   0xD7AA,
          0x8F6D,   0xE9EE,
          0x8F6E,   0xC2D6,
          0x8F6F,   0xC8ED,
          0x8F70,   0xBAE4,
          0x8F71,   0xE9EF,
          0x8F72,   0xE9F0,
          0x8F73,   0xE9F1,
          0x8F74,   0xD6E1,
          0x8F75,   0xE9F2,
          0x8F76,   0xE9F3,
          0x8F77,   0xE9F5,
          0x8F78,   0xE9F4,
          0x8F79,   0xE9F6,
          0x8F7A,   0xE9F7,
          0x8F7B,   0xC7E1,
          0x8F7C,   0xE9F8,
          0x8F7D,   0xD4D8,
          0x8F7E,   0xE9F9,
          0x8F7F,   0xBDCE,
          0x8F81,   0xE9FA,
          0x8F82,   0xE9FB,
          0x8F83,   0xBDCF,
          0x8F84,   0xE9FC,
          0x8F85,   0xB8A8,
          0x8F86,   0xC1BE,
          0x8F87,   0xE9FD,
          0x8F88,   0xB1B2,
          0x8F89,   0xBBD4,
          0x8F8A,   0xB9F5,
          0x8F8B,   0xE9FE,
          0x8F8D,   0xEAA1,
          0x8F8E,   0xEAA2,
          0x8F8F,   0xEAA3,
          0x8F90,   0xB7F8,
          0x8F91,   0xBCAD,
          0x8F93,   0xCAE4,
          0x8F94,   0xE0CE,
          0x8F95,   0xD4AF,
          0x8F96,   0xCFBD,
          0x8F97,   0xD5B7,
          0x8F98,   0xEAA4,
          0x8F99,   0xD5DE,
          0x8F9A,   0xEAA5,
          0x8F9B,   0xD0C1,
          0x8F9C,   0xB9BC,
          0x8F9E,   0xB4C7,
          0x8F9F,   0xB1D9,
          0x8FA3,   0xC0B1,
          0x8FA8,   0xB1E6,
          0x8FA9,   0xB1E7,
          0x8FAB,   0xB1E8,
          0x8FB0,   0xB3BD,
          0x8FB1,   0xC8E8,
          0x8FB6,   0xE5C1,
          0x8FB9,   0xB1DF,
          0x8FBD,   0xC1C9,
          0x8FBE,   0xB4EF,
          0x8FC1,   0xC7A8,
          0x8FC2,   0xD3D8,
          0x8FC4,   0xC6F9,
          0x8FC5,   0xD1B8,
          0x8FC7,   0xB9FD,
          0x8FC8,   0xC2F5,
          0x8FCE,   0xD3AD,
          0x8FD0,   0xD4CB,
          0x8FD1,   0xBDFC,
          0x8FD3,   0xE5C2,
          0x8FD4,   0xB7B5,
          0x8FD5,   0xE5C3,
          0x8FD8,   0xBBB9,
          0x8FD9,   0xD5E2,
          0x8FDB,   0xBDF8,
          0x8FDC,   0xD4B6,
          0x8FDD,   0xCEA5,
          0x8FDE,   0xC1AC,
          0x8FDF,   0xB3D9,
          0x8FE2,   0xCCF6,
          0x8FE4,   0xE5C6,
          0x8FE5,   0xE5C4,
          0x8FE6,   0xE5C8,
          0x8FE8,   0xE5CA,
          0x8FE9,   0xE5C7,
          0x8FEA,   0xB5CF,
          0x8FEB,   0xC6C8,
          0x8FED,   0xB5FC,
          0x8FEE,   0xE5C5,
          0x8FF0,   0xCAF6,
          0x8FF3,   0xE5C9,
          0x8FF7,   0xC3D4,
          0x8FF8,   0xB1C5,
          0x8FF9,   0xBCA3,
          0x8FFD,   0xD7B7,
          0x9000,   0xCDCB,
          0x9001,   0xCBCD,
          0x9002,   0xCACA,
          0x9003,   0xCCD3,
          0x9004,   0xE5CC,
          0x9005,   0xE5CB,
          0x9006,   0xC4E6,
          0x9009,   0xD1A1,
          0x900A,   0xD1B7,
          0x900B,   0xE5CD,
          0x900D,   0xE5D0,
          0x900F,   0xCDB8,
          0x9010,   0xD6F0,
          0x9011,   0xE5CF,
          0x9012,   0xB5DD,
          0x9014,   0xCDBE,
          0x9016,   0xE5D1,
          0x9017,   0xB6BA,
          0x901A,   0xCDA8,
          0x901B,   0xB9E4,
          0x901D,   0xCAC5,
          0x901E,   0xB3D1,
          0x901F,   0xCBD9,
          0x9020,   0xD4EC,
          0x9021,   0xE5D2,
          0x9022,   0xB7EA,
          0x9026,   0xE5CE,
          0x902D,   0xE5D5,
          0x902E,   0xB4FE,
          0x902F,   0xE5D6,
          0x9035,   0xE5D3,
          0x9036,   0xE5D4,
          0x9038,   0xD2DD,
          0x903B,   0xC2DF,
          0x903C,   0xB1C6,
          0x903E,   0xD3E2,
          0x9041,   0xB6DD,
          0x9042,   0xCBEC,
          0x9044,   0xE5D7,
          0x9047,   0xD3F6,
          0x904D,   0xB1E9,
          0x904F,   0xB6F4,
          0x9050,   0xE5DA,
          0x9051,   0xE5D8,
          0x9052,   0xE5D9,
          0x9053,   0xB5C0,
          0x9057,   0xD2C5,
          0x9058,   0xE5DC,
          0x905B,   0xE5DE,
          0x9062,   0xE5DD,
          0x9063,   0xC7B2,
          0x9065,   0xD2A3,
          0x9068,   0xE5DB,
          0x906D,   0xD4E2,
          0x906E,   0xD5DA,
          0x9074,   0xE5E0,
          0x9075,   0xD7F1,
          0x907D,   0xE5E1,
          0x907F,   0xB1DC,
          0x9080,   0xD1FB,
          0x9082,   0xE5E2,
          0x9083,   0xE5E4,
          0x9088,   0xE5E3,
          0x908B,   0xE5E5,
          0x9091,   0xD2D8,
          0x9093,   0xB5CB,
          0x9095,   0xE7DF,
          0x9097,   0xDAF5,
          0x9099,   0xDAF8,
          0x909B,   0xDAF6,
          0x909D,   0xDAF7,
          0x90A1,   0xDAFA,
          0x90A2,   0xD0CF,
          0x90A3,   0xC4C7,
          0x90A6,   0xB0EE,
          0x90AA,   0xD0B0,
          0x90AC,   0xDAF9,
          0x90AE,   0xD3CA,
          0x90AF,   0xBAAA,
          0x90B0,   0xDBA2,
          0x90B1,   0xC7F1,
          0x90B3,   0xDAFC,
          0x90B4,   0xDAFB,
          0x90B5,   0xC9DB,
          0x90B6,   0xDAFD,
          0x90B8,   0xDBA1,
          0x90B9,   0xD7DE,
          0x90BA,   0xDAFE,
          0x90BB,   0xC1DA,
          0x90BE,   0xDBA5,
          0x90C1,   0xD3F4,
          0x90C4,   0xDBA7,
          0x90C5,   0xDBA4,
          0x90C7,   0xDBA8,
          0x90CA,   0xBDBC,
          0x90CE,   0xC0C9,
          0x90CF,   0xDBA3,
          0x90D0,   0xDBA6,
          0x90D1,   0xD6A3,
          0x90D3,   0xDBA9,
          0x90D7,   0xDBAD,
          0x90DB,   0xDBAE,
          0x90DC,   0xDBAC,
          0x90DD,   0xBAC2,
          0x90E1,   0xBFA4,
          0x90E2,   0xDBAB,
          0x90E6,   0xDBAA,
          0x90E7,   0xD4C7,
          0x90E8,   0xB2BF,
          0x90EB,   0xDBAF,
          0x90ED,   0xB9F9,
          0x90EF,   0xDBB0,
          0x90F4,   0xB3BB,
          0x90F8,   0xB5A6,
          0x90FD,   0xB6BC,
          0x90FE,   0xDBB1,
          0x9102,   0xB6F5,
          0x9104,   0xDBB2,
          0x9119,   0xB1C9,
          0x911E,   0xDBB4,
          0x9122,   0xDBB3,
          0x9123,   0xDBB5,
          0x912F,   0xDBB7,
          0x9131,   0xDBB6,
          0x9139,   0xDBB8,
          0x9143,   0xDBB9,
          0x9146,   0xDBBA,
          0x9149,   0xD3CF,
          0x914A,   0xF4FA,
          0x914B,   0xC7F5,
          0x914C,   0xD7C3,
          0x914D,   0xC5E4,
          0x914E,   0xF4FC,
          0x914F,   0xF4FD,
          0x9150,   0xF4FB,
          0x9152,   0xBEC6,
          0x9157,   0xD0EF,
          0x915A,   0xB7D3,
          0x915D,   0xD4CD,
          0x915E,   0xCCAA,
          0x9161,   0xF5A2,
          0x9162,   0xF5A1,
          0x9163,   0xBAA8,
          0x9164,   0xF4FE,
          0x9165,   0xCBD6,
          0x9169,   0xF5A4,
          0x916A,   0xC0D2,
          0x916C,   0xB3EA,
          0x916E,   0xCDAA,
          0x916F,   0xF5A5,
          0x9170,   0xF5A3,
          0x9171,   0xBDB4,
          0x9172,   0xF5A8,
          0x9174,   0xF5A9,
          0x9175,   0xBDCD,
          0x9176,   0xC3B8,
          0x9177,   0xBFE1,
          0x9178,   0xCBE1,
          0x9179,   0xF5AA,
          0x917D,   0xF5A6,
          0x917E,   0xF5A7,
          0x917F,   0xC4F0,
          0x9185,   0xF5AC,
          0x9187,   0xB4BC,
          0x9189,   0xD7ED,
          0x918B,   0xB4D7,
          0x918C,   0xF5AB,
          0x918D,   0xF5AE,
          0x9190,   0xF5AD,
          0x9191,   0xF5AF,
          0x9192,   0xD0D1,
          0x919A,   0xC3D1,
          0x919B,   0xC8A9,
          0x91A2,   0xF5B0,
          0x91A3,   0xF5B1,
          0x91AA,   0xF5B2,
          0x91AD,   0xF5B3,
          0x91AE,   0xF5B4,
          0x91AF,   0xF5B5,
          0x91B4,   0xF5B7,
          0x91B5,   0xF5B6,
          0x91BA,   0xF5B8,
          0x91C7,   0xB2C9,
          0x91C9,   0xD3D4,
          0x91CA,   0xCACD,
          0x91CC,   0xC0EF,
          0x91CD,   0xD6D8,
          0x91CE,   0xD2B0,
          0x91CF,   0xC1BF,
          0x91D1,   0xBDF0,
          0x91DC,   0xB8AA,
          0x9274,   0xBCF8,
          0x928E,   0xF6C6,
          0x92AE,   0xF6C7,
          0x92C8,   0xF6C8,
          0x933E,   0xF6C9,
          0x936A,   0xF6CA,
          0x938F,   0xF6CC,
          0x93CA,   0xF6CB,
          0x93D6,   0xF7E9,
          0x943E,   0xF6CD,
          0x946B,   0xF6CE,
          0x9485,   0xEEC4,
          0x9486,   0xEEC5,
          0x9487,   0xEEC6,
          0x9488,   0xD5EB,
          0x9489,   0xB6A4,
          0x948A,   0xEEC8,
          0x948B,   0xEEC7,
          0x948C,   0xEEC9,
          0x948D,   0xEECA,
          0x948E,   0xC7A5,
          0x948F,   0xEECB,
          0x9490,   0xEECC,
          0x9492,   0xB7B0,
          0x9493,   0xB5F6,
          0x9494,   0xEECD,
          0x9495,   0xEECF,
          0x9497,   0xEECE,
          0x9499,   0xB8C6,
          0x949A,   0xEED0,
          0x949B,   0xEED1,
          0x949C,   0xEED2,
          0x949D,   0xB6DB,
          0x949E,   0xB3AE,
          0x949F,   0xD6D3,
          0x94A0,   0xC4C6,
          0x94A1,   0xB1B5,
          0x94A2,   0xB8D6,
          0x94A3,   0xEED3,
          0x94A4,   0xEED4,
          0x94A5,   0xD4BF,
          0x94A6,   0xC7D5,
          0x94A7,   0xBEFB,
          0x94A8,   0xCED9,
          0x94A9,   0xB9B3,
          0x94AA,   0xEED6,
          0x94AB,   0xEED5,
          0x94AC,   0xEED8,
          0x94AD,   0xEED7,
          0x94AE,   0xC5A5,
          0x94AF,   0xEED9,
          0x94B0,   0xEEDA,
          0x94B1,   0xC7AE,
          0x94B2,   0xEEDB,
          0x94B3,   0xC7AF,
          0x94B4,   0xEEDC,
          0x94B5,   0xB2A7,
          0x94B6,   0xEEDD,
          0x94B7,   0xEEDE,
          0x94B8,   0xEEDF,
          0x94B9,   0xEEE0,
          0x94BA,   0xEEE1,
          0x94BB,   0xD7EA,
          0x94BC,   0xEEE2,
          0x94BD,   0xEEE3,
          0x94BE,   0xBCD8,
          0x94BF,   0xEEE4,
          0x94C0,   0xD3CB,
          0x94C1,   0xCCFA,
          0x94C2,   0xB2AC,
          0x94C3,   0xC1E5,
          0x94C4,   0xEEE5,
          0x94C5,   0xC7A6,
          0x94C6,   0xC3AD,
          0x94C8,   0xEEE6,
          0x94C9,   0xEEE7,
          0x94CA,   0xEEE8,
          0x94CB,   0xEEE9,
          0x94CC,   0xEEEA,
          0x94CD,   0xEEEB,
          0x94CE,   0xEEEC,
          0x94D0,   0xEEED,
          0x94D1,   0xEEEE,
          0x94D2,   0xEEEF,
          0x94D5,   0xEEF0,
          0x94D6,   0xEEF1,
          0x94D7,   0xEEF2,
          0x94D8,   0xEEF4,
          0x94D9,   0xEEF3,
          0x94DB,   0xEEF5,
          0x94DC,   0xCDAD,
          0x94DD,   0xC2C1,
          0x94DE,   0xEEF6,
          0x94DF,   0xEEF7,
          0x94E0,   0xEEF8,
          0x94E1,   0xD5A1,
          0x94E2,   0xEEF9,
          0x94E3,   0xCFB3,
          0x94E4,   0xEEFA,
          0x94E5,   0xEEFB,
          0x94E7,   0xEEFC,
          0x94E8,   0xEEFD,
          0x94E9,   0xEFA1,
          0x94EA,   0xEEFE,
          0x94EB,   0xEFA2,
          0x94EC,   0xB8F5,
          0x94ED,   0xC3FA,
          0x94EE,   0xEFA3,
          0x94EF,   0xEFA4,
          0x94F0,   0xBDC2,
          0x94F1,   0xD2BF,
          0x94F2,   0xB2F9,
          0x94F3,   0xEFA5,
          0x94F4,   0xEFA6,
          0x94F5,   0xEFA7,
          0x94F6,   0xD2F8,
          0x94F7,   0xEFA8,
          0x94F8,   0xD6FD,
          0x94F9,   0xEFA9,
          0x94FA,   0xC6CC,
          0x94FC,   0xEFAA,
          0x94FD,   0xEFAB,
          0x94FE,   0xC1B4,
          0x94FF,   0xEFAC,
          0x9500,   0xCFFA,
          0x9501,   0xCBF8,
          0x9502,   0xEFAE,
          0x9503,   0xEFAD,
          0x9504,   0xB3FA,
          0x9505,   0xB9F8,
          0x9506,   0xEFAF,
          0x9507,   0xEFB0,
          0x9508,   0xD0E2,
          0x9509,   0xEFB1,
          0x950A,   0xEFB2,
          0x950B,   0xB7E6,
          0x950C,   0xD0BF,
          0x950D,   0xEFB3,
          0x950E,   0xEFB4,
          0x950F,   0xEFB5,
          0x9510,   0xC8F1,
          0x9511,   0xCCE0,
          0x9512,   0xEFB6,
          0x9513,   0xEFB7,
          0x9514,   0xEFB8,
          0x9515,   0xEFB9,
          0x9516,   0xEFBA,
          0x9517,   0xD5E0,
          0x9518,   0xEFBB,
          0x9519,   0xB4ED,
          0x951A,   0xC3AA,
          0x951B,   0xEFBC,
          0x951D,   0xEFBD,
          0x951E,   0xEFBE,
          0x951F,   0xEFBF,
          0x9521,   0xCEFD,
          0x9522,   0xEFC0,
          0x9523,   0xC2E0,
          0x9524,   0xB4B8,
          0x9525,   0xD7B6,
          0x9526,   0xBDF5,
          0x9528,   0xCFC7,
          0x9529,   0xEFC3,
          0x952A,   0xEFC1,
          0x952B,   0xEFC2,
          0x952C,   0xEFC4,
          0x952D,   0xB6A7,
          0x952E,   0xBCFC,
          0x952F,   0xBEE2,
          0x9530,   0xC3CC,
          0x9531,   0xEFC5,
          0x9532,   0xEFC6,
          0x9534,   0xEFC7,
          0x9535,   0xEFCF,
          0x9536,   0xEFC8,
          0x9537,   0xEFC9,
          0x9538,   0xEFCA,
          0x9539,   0xC7C2,
          0x953A,   0xEFF1,
          0x953B,   0xB6CD,
          0x953C,   0xEFCB,
          0x953E,   0xEFCC,
          0x953F,   0xEFCD,
          0x9540,   0xB6C6,
          0x9541,   0xC3BE,
          0x9542,   0xEFCE,
          0x9544,   0xEFD0,
          0x9545,   0xEFD1,
          0x9546,   0xEFD2,
          0x9547,   0xD5F2,
          0x9549,   0xEFD3,
          0x954A,   0xC4F7,
          0x954C,   0xEFD4,
          0x954D,   0xC4F8,
          0x954E,   0xEFD5,
          0x954F,   0xEFD6,
          0x9550,   0xB8E4,
          0x9551,   0xB0F7,
          0x9552,   0xEFD7,
          0x9553,   0xEFD8,
          0x9554,   0xEFD9,
          0x9556,   0xEFDA,
          0x9557,   0xEFDB,
          0x9558,   0xEFDC,
          0x9559,   0xEFDD,
          0x955B,   0xEFDE,
          0x955C,   0xBEB5,
          0x955D,   0xEFE1,
          0x955E,   0xEFDF,
          0x955F,   0xEFE0,
          0x9561,   0xEFE2,
          0x9562,   0xEFE3,
          0x9563,   0xC1CD,
          0x9564,   0xEFE4,
          0x9565,   0xEFE5,
          0x9566,   0xEFE6,
          0x9567,   0xEFE7,
          0x9568,   0xEFE8,
          0x9569,   0xEFE9,
          0x956A,   0xEFEA,
          0x956B,   0xEFEB,
          0x956C,   0xEFEC,
          0x956D,   0xC0D8,
          0x956F,   0xEFED,
          0x9570,   0xC1AD,
          0x9571,   0xEFEE,
          0x9572,   0xEFEF,
          0x9573,   0xEFF0,
          0x9576,   0xCFE2,
          0x957F,   0xB3A4,
          0x95E8,   0xC3C5,
          0x95E9,   0xE3C5,
          0x95EA,   0xC9C1,
          0x95EB,   0xE3C6,
          0x95ED,   0xB1D5,
          0x95EE,   0xCECA,
          0x95EF,   0xB4B3,
          0x95F0,   0xC8F2,
          0x95F1,   0xE3C7,
          0x95F2,   0xCFD0,
          0x95F3,   0xE3C8,
          0x95F4,   0xBCE4,
          0x95F5,   0xE3C9,
          0x95F6,   0xE3CA,
          0x95F7,   0xC3C6,
          0x95F8,   0xD5A2,
          0x95F9,   0xC4D6,
          0x95FA,   0xB9EB,
          0x95FB,   0xCEC5,
          0x95FC,   0xE3CB,
          0x95FD,   0xC3F6,
          0x95FE,   0xE3CC,
          0x9600,   0xB7A7,
          0x9601,   0xB8F3,
          0x9602,   0xBAD2,
          0x9603,   0xE3CD,
          0x9604,   0xE3CE,
          0x9605,   0xD4C4,
          0x9606,   0xE3CF,
          0x9608,   0xE3D0,
          0x9609,   0xD1CB,
          0x960A,   0xE3D1,
          0x960B,   0xE3D2,
          0x960C,   0xE3D3,
          0x960D,   0xE3D4,
          0x960E,   0xD1D6,
          0x960F,   0xE3D5,
          0x9610,   0xB2FB,
          0x9611,   0xC0BB,
          0x9612,   0xE3D6,
          0x9614,   0xC0AB,
          0x9615,   0xE3D7,
          0x9616,   0xE3D8,
          0x9617,   0xE3D9,
          0x9619,   0xE3DA,
          0x961A,   0xE3DB,
          0x961C,   0xB8B7,
          0x961D,   0xDAE2,
          0x961F,   0xB6D3,
          0x9621,   0xDAE4,
          0x9622,   0xDAE3,
          0x962A,   0xDAE6,
          0x962E,   0xC8EE,
          0x9631,   0xDAE5,
          0x9632,   0xB7C0,
          0x9633,   0xD1F4,
          0x9634,   0xD2F5,
          0x9635,   0xD5F3,
          0x9636,   0xBDD7,
          0x963B,   0xD7E8,
          0x963C,   0xDAE8,
          0x963D,   0xDAE7,
          0x963F,   0xB0A2,
          0x9640,   0xCDD3,
          0x9642,   0xDAE9,
          0x9644,   0xB8BD,
          0x9645,   0xBCCA,
          0x9646,   0xC2BD,
          0x9647,   0xC2A4,
          0x9648,   0xB3C2,
          0x9649,   0xDAEA,
          0x964B,   0xC2AA,
          0x964C,   0xC4B0,
          0x964D,   0xBDB5,
          0x9650,   0xCFDE,
          0x9654,   0xDAEB,
          0x9655,   0xC9C2,
          0x965B,   0xB1DD,
          0x965F,   0xDAEC,
          0x9661,   0xB6B8,
          0x9662,   0xD4BA,
          0x9664,   0xB3FD,
          0x9667,   0xDAED,
          0x9668,   0xD4C9,
          0x9669,   0xCFD5,
          0x966A,   0xC5E3,
          0x966C,   0xDAEE,
          0x9672,   0xDAEF,
          0x9674,   0xDAF0,
          0x9675,   0xC1EA,
          0x9676,   0xCCD5,
          0x9677,   0xCFDD,
          0x9685,   0xD3E7,
          0x9686,   0xC2A1,
          0x9688,   0xDAF1,
          0x968B,   0xCBE5,
          0x968D,   0xDAF2,
          0x968F,   0xCBE6,
          0x9690,   0xD2FE,
          0x9694,   0xB8F4,
          0x9697,   0xDAF3,
          0x9698,   0xB0AF,
          0x9699,   0xCFB6,
          0x969C,   0xD5CF,
          0x96A7,   0xCBED,
          0x96B0,   0xDAF4,
          0x96B3,   0xE3C4,
          0x96B6,   0xC1A5,
          0x96B9,   0xF6BF,
          0x96BC,   0xF6C0,
          0x96BD,   0xF6C1,
          0x96BE,   0xC4D1,
          0x96C0,   0xC8B8,
          0x96C1,   0xD1E3,
          0x96C4,   0xD0DB,
          0x96C5,   0xD1C5,
          0x96C6,   0xBCAF,
          0x96C7,   0xB9CD,
          0x96C9,   0xEFF4,
          0x96CC,   0xB4C6,
          0x96CD,   0xD3BA,
          0x96CE,   0xF6C2,
          0x96CF,   0xB3FB,
          0x96D2,   0xF6C3,
          0x96D5,   0xB5F1,
          0x96E0,   0xF6C5,
          0x96E8,   0xD3EA,
          0x96E9,   0xF6A7,
          0x96EA,   0xD1A9,
          0x96EF,   0xF6A9,
          0x96F3,   0xF6A8,
          0x96F6,   0xC1E3,
          0x96F7,   0xC0D7,
          0x96F9,   0xB1A2,
          0x96FE,   0xCEED,
          0x9700,   0xD0E8,
          0x9701,   0xF6AB,
          0x9704,   0xCFF6,
          0x9706,   0xF6AA,
          0x9707,   0xD5F0,
          0x9708,   0xF6AC,
          0x9709,   0xC3B9,
          0x970D,   0xBBF4,
          0x970E,   0xF6AE,
          0x970F,   0xF6AD,
          0x9713,   0xC4DE,
          0x9716,   0xC1D8,
          0x971C,   0xCBAA,
          0x971E,   0xCFBC,
          0x972A,   0xF6AF,
          0x972D,   0xF6B0,
          0x9730,   0xF6B1,
          0x9732,   0xC2B6,
          0x9738,   0xB0D4,
          0x9739,   0xC5F9,
          0x973E,   0xF6B2,
          0x9752,   0xC7E0,
          0x9753,   0xF6A6,
          0x9756,   0xBEB8,
          0x9759,   0xBEB2,
          0x975B,   0xB5E5,
          0x975E,   0xB7C7,
          0x9760,   0xBFBF,
          0x9761,   0xC3D2,
          0x9762,   0xC3E6,
          0x9765,   0xD8CC,
          0x9769,   0xB8EF,
          0x9773,   0xBDF9,
          0x9774,   0xD1A5,
          0x9776,   0xB0D0,
          0x977C,   0xF7B0,
          0x9785,   0xF7B1,
          0x978B,   0xD0AC,
          0x978D,   0xB0B0,
          0x9791,   0xF7B2,
          0x9792,   0xF7B3,
          0x9794,   0xF7B4,
          0x9798,   0xC7CA,
          0x97A0,   0xBECF,
          0x97A3,   0xF7B7,
          0x97AB,   0xF7B6,
          0x97AD,   0xB1DE,
          0x97AF,   0xF7B5,
          0x97B2,   0xF7B8,
          0x97B4,   0xF7B9,
          0x97E6,   0xCEA4,
          0x97E7,   0xC8CD,
          0x97E9,   0xBAAB,
          0x97EA,   0xE8B8,
          0x97EB,   0xE8B9,
          0x97EC,   0xE8BA,
          0x97ED,   0xBEC2,
          0x97F3,   0xD2F4,
          0x97F5,   0xD4CF,
          0x97F6,   0xC9D8,
          0x9875,   0xD2B3,
          0x9876,   0xB6A5,
          0x9877,   0xC7EA,
          0x9878,   0xF1FC,
          0x9879,   0xCFEE,
          0x987A,   0xCBB3,
          0x987B,   0xD0EB,
          0x987C,   0xE7EF,
          0x987D,   0xCDE7,
          0x987E,   0xB9CB,
          0x987F,   0xB6D9,
          0x9880,   0xF1FD,
          0x9881,   0xB0E4,
          0x9882,   0xCBCC,
          0x9883,   0xF1FE,
          0x9884,   0xD4A4,
          0x9885,   0xC2AD,
          0x9886,   0xC1EC,
          0x9887,   0xC6C4,
          0x9888,   0xBEB1,
          0x9889,   0xF2A1,
          0x988A,   0xBCD5,
          0x988C,   0xF2A2,
          0x988D,   0xF2A3,
          0x988F,   0xF2A4,
          0x9890,   0xD2C3,
          0x9891,   0xC6B5,
          0x9893,   0xCDC7,
          0x9894,   0xF2A5,
          0x9896,   0xD3B1,
          0x9897,   0xBFC5,
          0x9898,   0xCCE2,
          0x989A,   0xF2A6,
          0x989B,   0xF2A7,
          0x989C,   0xD1D5,
          0x989D,   0xB6EE,
          0x989E,   0xF2A8,
          0x989F,   0xF2A9,
          0x98A0,   0xB5DF,
          0x98A1,   0xF2AA,
          0x98A2,   0xF2AB,
          0x98A4,   0xB2FC,
          0x98A5,   0xF2AC,
          0x98A6,   0xF2AD,
          0x98A7,   0xC8A7,
          0x98CE,   0xB7E7,
          0x98D1,   0xECA9,
          0x98D2,   0xECAA,
          0x98D3,   0xECAB,
          0x98D5,   0xECAC,
          0x98D8,   0xC6AE,
          0x98D9,   0xECAD,
          0x98DA,   0xECAE,
          0x98DE,   0xB7C9,
          0x98DF,   0xCAB3,
          0x98E7,   0xE2B8,
          0x98E8,   0xF7CF,
          0x990D,   0xF7D0,
          0x9910,   0xB2CD,
          0x992E,   0xF7D1,
          0x9954,   0xF7D3,
          0x9955,   0xF7D2,
          0x9963,   0xE2BB,
          0x9965,   0xBCA2,
          0x9967,   0xE2BC,
          0x9968,   0xE2BD,
          0x9969,   0xE2BE,
          0x996A,   0xE2BF,
          0x996B,   0xE2C0,
          0x996C,   0xE2C1,
          0x996D,   0xB7B9,
          0x996E,   0xD2FB,
          0x996F,   0xBDA4,
          0x9970,   0xCACE,
          0x9971,   0xB1A5,
          0x9972,   0xCBC7,
          0x9974,   0xE2C2,
          0x9975,   0xB6FC,
          0x9976,   0xC8C4,
          0x9977,   0xE2C3,
          0x997A,   0xBDC8,
          0x997C,   0xB1FD,
          0x997D,   0xE2C4,
          0x997F,   0xB6F6,
          0x9980,   0xE2C5,
          0x9981,   0xC4D9,
          0x9984,   0xE2C6,
          0x9985,   0xCFDA,
          0x9986,   0xB9DD,
          0x9987,   0xE2C7,
          0x9988,   0xC0A1,
          0x998A,   0xE2C8,
          0x998B,   0xB2F6,
          0x998D,   0xE2C9,
          0x998F,   0xC1F3,
          0x9990,   0xE2CA,
          0x9991,   0xE2CB,
          0x9992,   0xC2F8,
          0x9993,   0xE2CC,
          0x9994,   0xE2CD,
          0x9995,   0xE2CE,
          0x9996,   0xCAD7,
          0x9997,   0xD8B8,
          0x9998,   0xD9E5,
          0x9999,   0xCFE3,
          0x99A5,   0xF0A5,
          0x99A8,   0xDCB0,
          0x9A6C,   0xC2ED,
          0x9A6D,   0xD4A6,
          0x9A6E,   0xCDD4,
          0x9A6F,   0xD1B1,
          0x9A70,   0xB3DB,
          0x9A71,   0xC7FD,
          0x9A73,   0xB2B5,
          0x9A74,   0xC2BF,
          0x9A75,   0xE6E0,
          0x9A76,   0xCABB,
          0x9A77,   0xE6E1,
          0x9A78,   0xE6E2,
          0x9A79,   0xBED4,
          0x9A7A,   0xE6E3,
          0x9A7B,   0xD7A4,
          0x9A7C,   0xCDD5,
          0x9A7D,   0xE6E5,
          0x9A7E,   0xBCDD,
          0x9A7F,   0xE6E4,
          0x9A80,   0xE6E6,
          0x9A81,   0xE6E7,
          0x9A82,   0xC2EE,
          0x9A84,   0xBDBE,
          0x9A85,   0xE6E8,
          0x9A86,   0xC2E6,
          0x9A87,   0xBAA7,
          0x9A88,   0xE6E9,
          0x9A8A,   0xE6EA,
          0x9A8B,   0xB3D2,
          0x9A8C,   0xD1E9,
          0x9A8F,   0xBFA5,
          0x9A90,   0xE6EB,
          0x9A91,   0xC6EF,
          0x9A92,   0xE6EC,
          0x9A93,   0xE6ED,
          0x9A96,   0xE6EE,
          0x9A97,   0xC6AD,
          0x9A98,   0xE6EF,
          0x9A9A,   0xC9A7,
          0x9A9B,   0xE6F0,
          0x9A9C,   0xE6F1,
          0x9A9D,   0xE6F2,
          0x9A9E,   0xE5B9,
          0x9A9F,   0xE6F3,
          0x9AA0,   0xE6F4,
          0x9AA1,   0xC2E2,
          0x9AA2,   0xE6F5,
          0x9AA3,   0xE6F6,
          0x9AA4,   0xD6E8,
          0x9AA5,   0xE6F7,
          0x9AA7,   0xE6F8,
          0x9AA8,   0xB9C7,
          0x9AB0,   0xF7BB,
          0x9AB1,   0xF7BA,
          0x9AB6,   0xF7BE,
          0x9AB7,   0xF7BC,
          0x9AB8,   0xBAA1,
          0x9ABA,   0xF7BF,
          0x9ABC,   0xF7C0,
          0x9AC0,   0xF7C2,
          0x9AC1,   0xF7C1,
          0x9AC2,   0xF7C4,
          0x9AC5,   0xF7C3,
          0x9ACB,   0xF7C5,
          0x9ACC,   0xF7C6,
          0x9AD1,   0xF7C7,
          0x9AD3,   0xCBE8,
          0x9AD8,   0xB8DF,
          0x9ADF,   0xF7D4,
          0x9AE1,   0xF7D5,
          0x9AE6,   0xF7D6,
          0x9AEB,   0xF7D8,
          0x9AED,   0xF7DA,
          0x9AEF,   0xF7D7,
          0x9AF9,   0xF7DB,
          0x9AFB,   0xF7D9,
          0x9B03,   0xD7D7,
          0x9B08,   0xF7DC,
          0x9B0F,   0xF7DD,
          0x9B13,   0xF7DE,
          0x9B1F,   0xF7DF,
          0x9B23,   0xF7E0,
          0x9B2F,   0xDBCB,
          0x9B32,   0xD8AA,
          0x9B3B,   0xE5F7,
          0x9B3C,   0xB9ED,
          0x9B41,   0xBFFD,
          0x9B42,   0xBBEA,
          0x9B43,   0xF7C9,
          0x9B44,   0xC6C7,
          0x9B45,   0xF7C8,
          0x9B47,   0xF7CA,
          0x9B48,   0xF7CC,
          0x9B49,   0xF7CB,
          0x9B4D,   0xF7CD,
          0x9B4F,   0xCEBA,
          0x9B51,   0xF7CE,
          0x9B54,   0xC4A7,
          0x9C7C,   0xD3E3,
          0x9C7F,   0xF6CF,
          0x9C81,   0xC2B3,
          0x9C82,   0xF6D0,
          0x9C85,   0xF6D1,
          0x9C86,   0xF6D2,
          0x9C87,   0xF6D3,
          0x9C88,   0xF6D4,
          0x9C8B,   0xF6D6,
          0x9C8D,   0xB1AB,
          0x9C8E,   0xF6D7,
          0x9C90,   0xF6D8,
          0x9C91,   0xF6D9,
          0x9C92,   0xF6DA,
          0x9C94,   0xF6DB,
          0x9C95,   0xF6DC,
          0x9C9A,   0xF6DD,
          0x9C9B,   0xF6DE,
          0x9C9C,   0xCFCA,
          0x9C9E,   0xF6DF,
          0x9C9F,   0xF6E0,
          0x9CA0,   0xF6E1,
          0x9CA1,   0xF6E2,
          0x9CA2,   0xF6E3,
          0x9CA3,   0xF6E4,
          0x9CA4,   0xC0F0,
          0x9CA5,   0xF6E5,
          0x9CA6,   0xF6E6,
          0x9CA7,   0xF6E7,
          0x9CA8,   0xF6E8,
          0x9CA9,   0xF6E9,
          0x9CAB,   0xF6EA,
          0x9CAD,   0xF6EB,
          0x9CAE,   0xF6EC,
          0x9CB0,   0xF6ED,
          0x9CB1,   0xF6EE,
          0x9CB2,   0xF6EF,
          0x9CB3,   0xF6F0,
          0x9CB4,   0xF6F1,
          0x9CB5,   0xF6F2,
          0x9CB6,   0xF6F3,
          0x9CB7,   0xF6F4,
          0x9CB8,   0xBEA8,
          0x9CBA,   0xF6F5,
          0x9CBB,   0xF6F6,
          0x9CBC,   0xF6F7,
          0x9CBD,   0xF6F8,
          0x9CC3,   0xC8FA,
          0x9CC4,   0xF6F9,
          0x9CC5,   0xF6FA,
          0x9CC6,   0xF6FB,
          0x9CC7,   0xF6FC,
          0x9CCA,   0xF6FD,
          0x9CCB,   0xF6FE,
          0x9CCC,   0xF7A1,
          0x9CCD,   0xF7A2,
          0x9CCE,   0xF7A3,
          0x9CCF,   0xF7A4,
          0x9CD0,   0xF7A5,
          0x9CD3,   0xF7A6,
          0x9CD4,   0xF7A7,
          0x9CD5,   0xF7A8,
          0x9CD6,   0xB1EE,
          0x9CD7,   0xF7A9,
          0x9CD8,   0xF7AA,
          0x9CD9,   0xF7AB,
          0x9CDC,   0xF7AC,
          0x9CDD,   0xF7AD,
          0x9CDE,   0xC1DB,
          0x9CDF,   0xF7AE,
          0x9CE2,   0xF7AF,
          0x9E1F,   0xC4F1,
          0x9E20,   0xF0AF,
          0x9E21,   0xBCA6,
          0x9E22,   0xF0B0,
          0x9E23,   0xC3F9,
          0x9E25,   0xC5B8,
          0x9E26,   0xD1BB,
          0x9E28,   0xF0B1,
          0x9E29,   0xF0B2,
          0x9E2A,   0xF0B3,
          0x9E2B,   0xF0B4,
          0x9E2C,   0xF0B5,
          0x9E2D,   0xD1BC,
          0x9E2F,   0xD1EC,
          0x9E31,   0xF0B7,
          0x9E32,   0xF0B6,
          0x9E33,   0xD4A7,
          0x9E35,   0xCDD2,
          0x9E36,   0xF0B8,
          0x9E37,   0xF0BA,
          0x9E38,   0xF0B9,
          0x9E39,   0xF0BB,
          0x9E3A,   0xF0BC,
          0x9E3D,   0xB8EB,
          0x9E3E,   0xF0BD,
          0x9E3F,   0xBAE8,
          0x9E41,   0xF0BE,
          0x9E42,   0xF0BF,
          0x9E43,   0xBEE9,
          0x9E44,   0xF0C0,
          0x9E45,   0xB6EC,
          0x9E46,   0xF0C1,
          0x9E47,   0xF0C2,
          0x9E48,   0xF0C3,
          0x9E49,   0xF0C4,
          0x9E4A,   0xC8B5,
          0x9E4B,   0xF0C5,
          0x9E4C,   0xF0C6,
          0x9E4E,   0xF0C7,
          0x9E4F,   0xC5F4,
          0x9E51,   0xF0C8,
          0x9E55,   0xF0C9,
          0x9E57,   0xF0CA,
          0x9E58,   0xF7BD,
          0x9E5A,   0xF0CB,
          0x9E5B,   0xF0CC,
          0x9E5C,   0xF0CD,
          0x9E5E,   0xF0CE,
          0x9E63,   0xF0CF,
          0x9E64,   0xBAD7,
          0x9E66,   0xF0D0,
          0x9E67,   0xF0D1,
          0x9E68,   0xF0D2,
          0x9E69,   0xF0D3,
          0x9E6A,   0xF0D4,
          0x9E6B,   0xF0D5,
          0x9E6C,   0xF0D6,
          0x9E6D,   0xF0D8,
          0x9E70,   0xD3A5,
          0x9E71,   0xF0D7,
          0x9E73,   0xF0D9,
          0x9E7E,   0xF5BA,
          0x9E7F,   0xC2B9,
          0x9E82,   0xF7E4,
          0x9E87,   0xF7E5,
          0x9E88,   0xF7E6,
          0x9E8B,   0xF7E7,
          0x9E92,   0xF7E8,
          0x9E93,   0xC2B4,
          0x9E9D,   0xF7EA,
          0x9E9F,   0xF7EB,
          0x9EA6,   0xC2F3,
          0x9EB4,   0xF4F0,
          0x9EB8,   0xF4EF,
          0x9EBB,   0xC2E9,
          0x9EBD,   0xF7E1,
          0x9EBE,   0xF7E2,
          0x9EC4,   0xBBC6,
          0x9EC9,   0xD9E4,
          0x9ECD,   0xCAF2,
          0x9ECE,   0xC0E8,
          0x9ECF,   0xF0A4,
          0x9ED1,   0xBADA,
          0x9ED4,   0xC7AD,
          0x9ED8,   0xC4AC,
          0x9EDB,   0xF7EC,
          0x9EDC,   0xF7ED,
          0x9EDD,   0xF7EE,
          0x9EDF,   0xF7F0,
          0x9EE0,   0xF7EF,
          0x9EE2,   0xF7F1,
          0x9EE5,   0xF7F4,
          0x9EE7,   0xF7F3,
          0x9EE9,   0xF7F2,
          0x9EEA,   0xF7F5,
          0x9EEF,   0xF7F6,
          0x9EF9,   0xEDE9,
          0x9EFB,   0xEDEA,
          0x9EFC,   0xEDEB,
          0x9EFE,   0xF6BC,
          0x9F0B,   0xF6BD,
          0x9F0D,   0xF6BE,
          0x9F0E,   0xB6A6,
          0x9F10,   0xD8BE,
          0x9F13,   0xB9C4,
          0x9F17,   0xD8BB,
          0x9F19,   0xDCB1,
          0x9F20,   0xCAF3,
          0x9F22,   0xF7F7,
          0x9F2C,   0xF7F8,
          0x9F2F,   0xF7F9,
          0x9F37,   0xF7FB,
          0x9F39,   0xF7FA,
          0x9F3B,   0xB1C7,
          0x9F3D,   0xF7FC,
          0x9F3E,   0xF7FD,
          0x9F44,   0xF7FE,
          0x9F50,   0xC6EB,
          0x9F51,   0xECB4,
          0x9F7F,   0xB3DD,
          0x9F80,   0xF6B3,
          0x9F83,   0xF6B4,
          0x9F84,   0xC1E4,
          0x9F85,   0xF6B5,
          0x9F86,   0xF6B6,
          0x9F87,   0xF6B7,
          0x9F88,   0xF6B8,
          0x9F89,   0xF6B9,
          0x9F8A,   0xF6BA,
          0x9F8B,   0xC8A3,
          0x9F8C,   0xF6BB,
          0x9F99,   0xC1FA,
          0x9F9A,   0xB9A8,
          0x9F9B,   0xEDE8,
          0x9F9F,   0xB9EA,
          0x9FA0,   0xD9DF,
          0xFF01,   0xA3A1,
          0xFF02,   0xA3A2,
          0xFF03,   0xA3A3,
          0xFF04,   0xA1E7,
          0xFF05,   0xA3A5,
          0xFF06,   0xA3A6,
          0xFF07,   0xA3A7,
          0xFF08,   0xA3A8,
          0xFF09,   0xA3A9,
          0xFF0A,   0xA3AA,
          0xFF0B,   0xA3AB,
          0xFF0C,   0xA3AC,
          0xFF0D,   0xA3AD,
          0xFF0E,   0xA3AE,
          0xFF0F,   0xA3AF,
          0xFF10,   0xA3B0,
          0xFF11,   0xA3B1,
          0xFF12,   0xA3B2,
          0xFF13,   0xA3B3,
          0xFF14,   0xA3B4,
          0xFF15,   0xA3B5,
          0xFF16,   0xA3B6,
          0xFF17,   0xA3B7,
          0xFF18,   0xA3B8,
          0xFF19,   0xA3B9,
          0xFF1A,   0xA3BA,
          0xFF1B,   0xA3BB,
          0xFF1C,   0xA3BC,
          0xFF1D,   0xA3BD,
          0xFF1E,   0xA3BE,
          0xFF1F,   0xA3BF,
          0xFF20,   0xA3C0,
          0xFF21,   0xA3C1,
          0xFF22,   0xA3C2,
          0xFF23,   0xA3C3,
          0xFF24,   0xA3C4,
          0xFF25,   0xA3C5,
          0xFF26,   0xA3C6,
          0xFF27,   0xA3C7,
          0xFF28,   0xA3C8,
          0xFF29,   0xA3C9,
          0xFF2A,   0xA3CA,
          0xFF2B,   0xA3CB,
          0xFF2C,   0xA3CC,
          0xFF2D,   0xA3CD,
          0xFF2E,   0xA3CE,
          0xFF2F,   0xA3CF,
          0xFF30,   0xA3D0,
          0xFF31,   0xA3D1,
          0xFF32,   0xA3D2,
          0xFF33,   0xA3D3,
          0xFF34,   0xA3D4,
          0xFF35,   0xA3D5,
          0xFF36,   0xA3D6,
          0xFF37,   0xA3D7,
          0xFF38,   0xA3D8,
          0xFF39,   0xA3D9,
          0xFF3A,   0xA3DA,
          0xFF3B,   0xA3DB,
          0xFF3C,   0xA3DC,
          0xFF3D,   0xA3DD,
          0xFF3E,   0xA3DE,
          0xFF3F,   0xA3DF,
          0xFF40,   0xA3E0,
          0xFF41,   0xA3E1,
          0xFF42,   0xA3E2,
          0xFF43,   0xA3E3,
          0xFF44,   0xA3E4,
          0xFF45,   0xA3E5,
          0xFF46,   0xA3E6,
          0xFF47,   0xA3E7,
          0xFF48,   0xA3E8,
          0xFF49,   0xA3E9,
          0xFF4A,   0xA3EA,
          0xFF4B,   0xA3EB,
          0xFF4C,   0xA3EC,
          0xFF4D,   0xA3ED,
          0xFF4E,   0xA3EE,
          0xFF4F,   0xA3EF,
          0xFF50,   0xA3F0,
          0xFF51,   0xA3F1,
          0xFF52,   0xA3F2,
          0xFF53,   0xA3F3,
          0xFF54,   0xA3F4,
          0xFF55,   0xA3F5,
          0xFF56,   0xA3F6,
          0xFF57,   0xA3F7,
          0xFF58,   0xA3F8,
          0xFF59,   0xA3F9,
          0xFF5A,   0xA3FA,
          0xFF5B,   0xA3FB,
          0xFF5C,   0xA3FC,
          0xFF5D,   0xA3FD,
          0xFF5E,   0xA1AB,
          0xFFE0,   0xA1E9,
          0xFFE1,   0xA1EA,
          0xFFE3,   0xA3FE,
          0xFFE5,   0xA3A4
        };
        
INT16U GetGBCode(INT16U unicode)
{
    INT16U begtag, endtag, curtag, curcode;
    
    begtag = 0;
    endtag = sizeof(UN_GBTB) / 4 - 1;
    for (;;) {
        curtag  = (begtag + endtag) / 2;
        curcode = UN_GBTB[2 * curtag];
        if (curcode == unicode) {
            return UN_GBTB[2 * curtag + 1];
        }
        if (begtag == endtag) {
            return 0x3030;
        }
        if (curcode > unicode) {
            endtag = curtag;
        } else {
            begtag = curtag + 1;
        }
    }
}

INT8U UniToGB(INT8U *dptr, INT8U *sptr, INT8U len)
{
    INT16U gbcode;
    INT8U  rlen;
    
    if (len % 2) return 0;
    for (len /= 2, rlen = 0; len > 0; len--) {
        gbcode  = *sptr++ << 8;
        gbcode += *sptr++;
        if (gbcode < 0x0080) {
            *dptr++ = gbcode;
            rlen++;
        } else {
            if ((gbcode = GetGBCode(gbcode)) == 0x3030) {
            	*dptr++ = '?';
            	rlen++;
            } else {
            	*dptr++ = gbcode >> 8;
            	*dptr++ = gbcode;
            	rlen   += 2;
            }
        }
    }
    return rlen;
}

