ifeq ($(PARAM_FILE), )
     PARAM_FILE:=../Makefile.param
     include $(PARAM_FILE)
endif

ifeq ($(APP_PARAM_FILE), )
     APP_PARAM_FILE:=../Makefile.base
     include $(APP_PARAM_FILE)
endif

#define complier
#ARM_COMPILER = arm-oe-linux-gnueabi-

# config version
PLATFORM=
APPTYPE=tbox.exe
VERSION=

# target
TARGET		:= $(strip $(PLATFORM))$(strip $(APPTYPE))$(strip $(VERSION))

# define build template, app / lib
TEMPLATE	:= app

# define configure, static / shared if lib
CONFIG		+= static

# default install dir
BINDIR		?= $(APP_PATH)/../customapps/app/
DEBUG_DIR	?=/mnt/d_win/tools/work\ tools/SecureCRT-v6.1.4H/SecureCRTchs/upload/
ROOTFS_APPDIR	?= $(APP_PATH)/../ql-ol-rootfs/custom/flash/app/
	
# external libraries


LIBS	   += -L../depend/lib/ 
LIBS        +=-lcurl
#LIBS       +=-L../libpki/pki_sdk/out/release/new_ca/  -lssptbox_HANRJ


# defines
DEFINES		+= 

# compile flags
#CFLAGS		+= -Wall -O2 -Wno-uninitialized -fno-strict-aliasing
CFLAGS		+= -mapcs -rdynamic -Wall -O2 -Wno-uninitialized -Wno-missing-braces -fno-strict-aliasing

LDFLAGS    +=

include ./Makefile.inc
#include $(APP_PATH)/gsmat/ext_make/Makefile.pathgsm
include ./Makefile.template
