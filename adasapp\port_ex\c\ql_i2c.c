#include <stdio.h>
#include <stdlib.h>
#include <errno.h>
#include <string.h>
#include <linux/i2c.h>
#include <linux/i2c-dev.h>
#include <sys/ioctl.h>
#include <fcntl.h>
#include <unistd.h>

int ql_i2c_init(char *dev_name)
{
    if ((NULL == dev_name) || (*dev_name == '\0'))
    {
        return -1;
    }
    int fd_i2c = open(dev_name, O_RDWR);
    printf("< open(%s, O_RDWR)=%d >\n", dev_name, fd_i2c);
    if (fd_i2c < 0)  
    {
        printf("< Fail to open i2c >\n");
        return -1;  
    }  
    return fd_i2c;
}

int ql_i2c_read(int fd, unsigned short slaveAddr, unsigned char ofstAddr,  unsigned char* ptrBuff, unsigned short length)
{
    int iRet = 0;
    
    struct i2c_msg i2c_msgs[] = {
        [0] = {
            .addr  = slaveAddr,
            .flags = 0, // write
            .buf   = &ofstAddr,
            .len   = 1,
        },
        [1] = {
            .addr  = slaveAddr,
            .flags = I2C_M_RD,
            .buf   = ptrBuff,
            .len   = length, 
        },
    };

    struct i2c_rdwr_ioctl_data msgset = {
        .msgs = i2c_msgs,
        .nmsgs = 2,
    };

    iRet = ioctl(fd, I2C_RDWR, &msgset);
    if (iRet < 0)
    {
        printf("%s, read failed rc : %d \n", __FUNCTION__, iRet);
        return -1;
    }

    return 0;
}

int ql_i2c_write(int fd, unsigned short slaveAddr, unsigned char ofstAddr,  unsigned char* ptrData, unsigned short length)
{
    int iRet = 0;
    unsigned char *write_buff = (unsigned char *)malloc(sizeof(unsigned char)*(length+1));
    if(write_buff == NULL)
    {
        printf("malloc error");
        return -1;
    }

    //pack the data
    write_buff[0] = ofstAddr;
    memcpy(write_buff+1, ptrData, length);

    struct i2c_msg i2c_msgs = {
        .addr  = slaveAddr,
        .flags = 0, // write
        .buf   = write_buff,
        .len   = length + 1,
    };
    struct i2c_rdwr_ioctl_data msgset = {
        .msgs  = &i2c_msgs,
        .nmsgs = 1,
    };
    
    iRet = ioctl(fd, I2C_RDWR, &msgset);
    if (iRet < 0)
    {
        printf("%s, write failed, iRet=%d \n", __FUNCTION__, iRet);
        return -1;
    }

    free(write_buff);
    write_buff = NULL;

    return 0;
}

int ql_i2c_deinit(int fd)
{
    close(fd);
    return 0;
}