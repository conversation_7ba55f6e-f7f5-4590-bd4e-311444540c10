

#ifndef _H_MD5_
#define _H_MD5_

/* Data structure for MD5 (Message-Digest) computation */
typedef struct {
  unsigned int i[2];        /* number of _bits_ handled mod 2^64 */
  unsigned int buf[4];      /* scratch buffer */
  unsigned char in[64];     /* input buffer */
  unsigned char digest[16]; /* actual digest after MD5Final call */
} MD5_CTX;

void MD5Init(MD5_CTX *mdContext);
void MD5Update(MD5_CTX *mdContext, unsigned char *inBuf, unsigned int inLen);
void MD5Final(unsigned char hash[], MD5_CTX *mdContext);


#endif

