
# Use this file as the following sample
# ifeq ($(APP_PARAM_FILE), )
#     APP_PARAM_FILE:=../Makefile.base
#     include $(APP_PARAM_FILE)
# endif

export APP_PARAM_FILE
export APP_PATH?=$(shell cd $(PWD)/`dirname $(APP_PARAM_FILE)`/..; pwd)
export APP_PATH_ENTRY?=$(shell cd $(PWD)/`dirname $(APP_PARAM_FILE)`/; pwd)

INCLUDES += -I$(REL_INC)
INCLUDES += -I$(REL_INC)/extdrv
INCLUDES += -I$(APP_PATH_ENTRY)/comuserv/h

CFLAGS   += -L$(REL_LIB)
CFLAGS   += -L$(APP_PATH_ENTRY)/comuserv/_build
CFLAGS   += -L$(REL_LIB)/extdrv

LDFLAGS  += -L$(REL_LIB)
LDFLAGS  += -L$(APP_PATH_ENTRY)/comuserv/_build
LDFLAGS  += -L$(REL_LIB)/extdrv
