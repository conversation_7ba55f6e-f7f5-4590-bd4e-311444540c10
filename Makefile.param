# Makefile.param 
# Only global variable should be defined here.
# All the variables must be used as "export" and "?=".
# Otherwise, there will be some errors, when Makefile.param is nested.

# Use this file as the following sample
# ifeq ($(PARAM_FILE), )
#     PARAM_FILE:=../Makefile.param
#     include $(PARAM_FILE)
# endif

# Define the default OS link directory.

export PARAM_FILE

export PLAT_PATH        := /opt/ql_crosstools/
#musl lib
export CROSSTOOL_PATH   := $(PLAT_PATH)/ql-ec200a-1803e-gcc-8.4.0-v1-toolchain/
#export SYSROOT_DIR      := ../../../ql-sysroots
export SYSROOT_DIR      := /opt/ql_crosstools/ec200acnta/r03a01/ql-sysroots
#export SYSROOT_DIR      := /mnt/d_win/program/asr/ec200a-ta/ql-ol-extsdk/ql-sysroots
#glibc lib
#export CROSSTOOL_PATH   := $(PLAT_PATH)/ql-ec200a-1803e-gcc-glibc-8.4.0-v1-toolchain/
#export SYSROOT_DIR      := /work/project/plat/ec200acnta/ql-ol-extsdk-ec200acntar03a01m2g_ocpu_beta0516/ql-sysroots

export STAGING_DIR      := $(CROSSTOOL_PATH)/bin:$(TAGING_DIR)
export CFLAGS           += --sysroot=$(SYSROOT_DIR)/
export LDFLAGS          += -L$(SYSROOT_DIR)/usr/lib
#musl lib
export LDFLAGS          += -L$(SYSROOT_DIR)/lib
#glibc lib
#export LDFLAGS          += -L$(SYSROOT_DIR)/lib
export LDFLAGS          += -lql_sdk -lpthread  -lql_lib_ipc -lql_lib_utils  -lql_sdk_cmpt  -lql_sys_log   -llog  -lcurl
export LDFLAGS          += -lql_lib_absys -lql_lib_fota  -lubus -lrilutil -lblobmsg_json
export CFLAGS           += -I$(SYSROOT_DIR)/usr/include
export CFLAGS           += -I$(SYSROOT_DIR)/usr/include/curl
export CFLAGS           += -I$(SYSROOT_DIR)/usr/include/ql-sdk
export CFLAGS           += -I$(SYSROOT_DIR)/usr/include/ql-sdk-cmpt
export CFLAGS           +=  -march=armv7-a -marm -mfpu=neon -mfloat-abi=hard


# Define cross compiler
export ARM_COMPILER=$(CROSSTOOL_PATH)/bin/arm-openwrt-linux-
