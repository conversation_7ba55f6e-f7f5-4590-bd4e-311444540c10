_temp/port/c/YX_PORT.o _temp/port/c/YX_PORT.d : port/c/YX_PORT.c includes/yx_linux_includes.h \
 port/h/yx_port_config.h includes/yx_system.h includes/yx_structs.h \
 includes/yx_includes.h includes/yx_system.h includes/yx_swconfig.h \
 includes/yx_structs.h includes/yx_linux_includes.h port/h/yx_port_gpio.h \
 port/h/yx_port_config.h includes/yx_swconfig.h port/h/yx_port.h \
 port/h/yx_port_sock.h port/h/yx_port_debug.h port/h/yx_port.h \
 port/h/yx_port_msghdl.h kernel/h/yx_timer.h kernel/h/yx_diagnose.h \
 includes/yx_includes.h kernel/h/yx_errcode.h kernel/h/yx_debug.h \
 /opt/ql_crosstools/ec200acnta/r03a01/ql-sysroots/usr/include/ql-sdk-cmpt/ql_oe.h \
 /opt/ql_crosstools/ec200acnta/r03a01/ql-sysroots/usr/include/ql-sdk-cmpt/ql_error.h \
 /opt/ql_crosstools/ec200acnta/r03a01/ql-sysroots/usr/include/ql-sdk-cmpt/ql_sleep_wakelock.h \
 /opt/ql_crosstools/ec200acnta/r03a01/ql-sysroots/usr/include/ql-sdk-cmpt/ql_nslookup.h \
 /opt/ql_crosstools/ec200acnta/r03a01/ql-sysroots/usr/include/ql-sdk-cmpt/ql_wwan_v2.h \
 /opt/ql_crosstools/ec200acnta/r03a01/ql-sysroots/usr/include/ql-sdk-cmpt/ql_mcm.h \
 /opt/ql_crosstools/ec200acnta/r03a01/ql-sysroots/usr/include/ql-sdk-cmpt/ql_mcm_sim.h \
 /opt/ql_crosstools/ec200acnta/r03a01/ql-sysroots/usr/include/ql-sdk-cmpt/ql_mcm_nw.h \
 /opt/ql_crosstools/ec200acnta/r03a01/ql-sysroots/usr/include/ql-sdk-cmpt/ql_mcm_atc.h \
 /opt/ql_crosstools/ec200acnta/r03a01/ql-sysroots/usr/include/ql-sdk-cmpt/ql_mcm_sms.h \
 /opt/ql_crosstools/ec200acnta/r03a01/ql-sysroots/usr/include/ql-sdk-cmpt/ql_mcm_voice.h \
 /opt/ql_crosstools/ec200acnta/r03a01/ql-sysroots/usr/include/ql-sdk-cmpt/ql_mcm_dm.h \
 /opt/ql_crosstools/ec200acnta/r03a01/ql-sysroots/usr/include/ql-sdk-cmpt/ql_lpm.h \
 /opt/ql_crosstools/ec200acnta/r03a01/ql-sysroots/usr/include/ql-sdk/ql_lpm.h \
 /opt/ql_crosstools/ec200acnta/r03a01/ql-sysroots/usr/include/ql-sdk-cmpt/ql_gpio.h
