/*******************************************************************
 *
 *   文件名:     YX_PORT_MsgHdl.C
 *   文件描述:   系统消息入口
 *
 *******************************************************************/
#include "yx_linux_includes.h"

#include "yx_port.h"
#include "yx_port_event.h"
#include "yx_msgman.h"
#include "yx_gprsdrv.h"
#include "yx_port_gprs.h"
#include "yx_diagnose.h"
#include "yx_port_msghdl.h"
#include "yx_port_sock.h"
#include "yx_timer.h"

static int port_sock_msg_fd[2];

BOOLEAN YX_PORT_Send(PORT_MSG_T msg)
{
    if ( write(port_sock_msg_fd[0] , &msg , sizeof(msg) )  != sizeof(msg)) 
    {
        return FALSE;
    }
    else 
    {
        return TRUE;
    }
}

static void YX_Port_MsgHdl(INT32S fd, void *param, INT8U event)
{
    PORT_MSG_T  msg;

	if (event != EVENT_RD)
		return;
    if (read(port_sock_msg_fd[1], &msg, sizeof(msg)) == sizeof(msg))
    {
        switch (msg.msgtype) {
            case PORT_MSG_COMMON:
                YX_HdlMsg();
                break;
			case PORT_MSG_GPRS_INIT:
                YX_InformerGprsInit(msg.result);
                break;
            case PORT_MSG_GPRS_ATTACH:
                YX_InformerGprsAttach(msg.result);
                break;
            case PORT_MSG_GPRS_APNGET:
                YX_InformerGprsApnGet(msg.result);
                break;
            case PORT_MSG_GPRS_ACTIVED:
                YX_InformGprsActivated_DataCall(msg.contextid);// add by lxl
                break;
            case PORT_MSG_GPRS_DEACTIVED:
                YX_InformGprsActivated_DataStop(msg.contextid);
                YX_InformGprsDeactivated(0);
                break;
            case PORT_MSG_GPRS_GETHOSTIPBYNAME:
                YX_InformGetIpbyName(msg.result, msg.data);
                break;
            case PORT_MSG_TIMER:
                YX_InterfaceTmrEntry();
            default:
                break;
        }
    }

}


void YX_PortLayerInit(void)
{
    ASSERT((socketpair(AF_UNIX, SOCK_STREAM,0, port_sock_msg_fd) ) == 0,  ERR_PORT_MSGHDL);

    YX_Port_EventRegister(EVENT_RD|EVENT_ERR, port_sock_msg_fd[1], YX_Port_MsgHdl, NULL);

    YX_PORT_Init();
}


