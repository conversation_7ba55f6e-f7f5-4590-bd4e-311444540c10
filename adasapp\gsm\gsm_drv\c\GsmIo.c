
#include "yx_includes.h"
#include "yx_tools.h"
#include "yx_timer.h"
#include "yx_msgman.h"
#include "yx_debug.h"
#include "yx_uart.h"
#include "gsmio.h"
#include "yx_virgsmtsk.h"

#define PERIOD_GSMIO_SCAN               MILTICK, 2

static INT8U gsmioscantmr;
static INT8U gsmiobuffer[512];
static INT16S gsmiolen, prebyte;

static void GsmIoScanTmrProc(void *pdata)
{
    INT16S curbyte;

    while ((curbyte = YX_UartRead(GSMIO_UART_NO)) != -1) {
        gsmiobuffer[gsmiolen++] = curbyte;
        if (curbyte == LF) {
            if (prebyte == CR) {
                if (gsmiolen > 3) {
#if DEBUG_GSMATCMD > 0
                    debug_printf("\r\n");
                    printf_mem(gsmiobuffer, gsmiolen);
#endif
                    HdlMsg_ATCMD_RECV(gsmiobuffer, gsmiolen);
                }
                gsmiolen = 0;
                prebyte  = 0;
            } else {
                prebyte = curbyte;
            }
        } else {
            prebyte = curbyte;
        }
        if (gsmiolen >= sizeof(gsmiobuffer)) {
#if DEBUG_GSMSTATUS > 0
    debug_printf("GsmIoScanTmrProc:recv message is too long!!!!!!!!!!!!!!\r\n");
    printf_mem(gsmiobuffer, gsmiolen);
#endif
            gsmiolen = 0;
        }     
    }

}

void InitGsmIo(void)
{
    gsmiolen = 0;
    prebyte  = 0;
    YX_InitUart(GSMIO_UART_NO, GSMIO_UART_BAUD);
    gsmioscantmr = YX_InstallTmr(PRIO_GSMTASK, (void *)0, GsmIoScanTmrProc);
    YX_StartTmr(gsmioscantmr, PERIOD_GSMIO_SCAN);
}

