

#include <stdio.h>
#include <string.h>
#include <stdlib.h>

#include "ql_app_feature_config.h"
#include "ql_api_osi.h"
#include "ql_log.h"
#include "ql_pin_config.h"
#include "ql_api_dev.h"
#include "ql_fs.h"


#define QL_INIT_LOG_LEVEL	QL_LOG_LEVEL_INFO
#define QL_INIT_LOG(msg, ...)			QL_LOG(QL_INIT_LOG_LEVEL, "ql_INIT", msg, ##__VA_ARGS__)
#define QL_INIT_LOG_PUSH(msg, ...)	QL_LOG_PUSH("ql_INIT", msg, ##__VA_ARGS__)

static void ql_pin_cfg_init(void)
{
    uint8_t       index        = 0;
    uint8_t       pin_num      = 0;
    uint8_t       default_func = 0;
    uint8_t       gpio_func    = 0;
    ql_GpioNum    gpio_num     = 0;
    ql_GpioDir    gpio_dir     = 0;
    ql_PullMode   gpio_pull    = 0;
    ql_LvlMode    gpio_lvl     = 0;

    for( index = 0; index < QL_GPIO_PIN_MAX; index++ )
    {
        //QL_INIT_LOG("pin%d=%d", index, ql_pin_cfg_map[index].pin_num);
	    if (QUEC_PIN_NONE == ql_pin_cfg_map[index].pin_num)
        {
            //QL_INIT_LOG("init exit %d!", index);
            break;
        }
        pin_num      = ql_pin_cfg_map[index].pin_num;
        default_func = ql_pin_cfg_map[index].default_func;
        gpio_func    = ql_pin_cfg_map[index].gpio_func;
        gpio_num     = ql_pin_cfg_map[index].gpio_num;
        gpio_dir     = ql_pin_cfg_map[index].gpio_dir;
        gpio_pull    = ql_pin_cfg_map[index].gpio_pull;
        gpio_lvl     = ql_pin_cfg_map[index].gpio_lvl;

        ql_pin_set_func(pin_num, default_func);
        if( default_func == gpio_func )
        {
            ql_gpio_init(gpio_num, gpio_dir, gpio_pull, gpio_lvl);
        }
    }
}

static void prvInvokeGlobalCtors(void)
{
    extern void (*__init_array_start[])();
    extern void (*__init_array_end[])();

    size_t count = __init_array_end - __init_array_start;
    for (size_t i = 0; i < count; ++i)
        __init_array_start[i]();
}

int appimg_enter(void *param)
{
    QL_INIT_LOG("init demo enter: %s @ %s", QL_APP_VERSION, QL_APP_BUILD_RELEASE_TYPE);
    prvInvokeGlobalCtors();
    if(0 == strcasecmp(QL_APP_BUILD_RELEASE_TYPE, "release"))
    {
    	ql_dev_cfg_wdt(1);
        //open the kernel log
    	//ql_quec_trace_enable(1);
    }
    else
    {
    	ql_dev_cfg_wdt(0);
        //close the kernel log
    	//ql_quec_trace_enable(0);
    }

    /*Caution: GPIO pin must be initialized here, otherwise the pin status cannot be determined*/
    ql_pin_cfg_init();
 
    extern int main_entry(void *param);
    return main_entry(param);
}

void appimg_exit(void)
{
    QL_INIT_LOG("init demo exit");
}


