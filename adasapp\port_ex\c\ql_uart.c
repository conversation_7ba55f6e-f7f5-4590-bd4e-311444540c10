#include <pthread.h>
#include <sys/stat.h>
#include <fcntl.h>
#include <stdlib.h>
#include <string.h>
#include <sys/select.h>
#include <sys/time.h>
#include <sys/types.h>
#include <unistd.h>
#include <stdio.h>
#include <unistd.h>
#include <strings.h>
#include <errno.h>
#include <termios.h>
#include <unistd.h>
#include <sys/ioctl.h>

#include "ql-sdk/ql_type.h"
#include "ql_uart.h"


#define UART_WRITE_MAX_SIZE 64

static int buildBaudrate(Enum_BaudRate baudrate, struct termios* term);
static Enum_BaudRate getBaudrate(struct termios* term);

static int buildDataBit(Enum_DataBit databit, struct termios* term);
static Enum_DataBit getDataBit(struct termios* term);

static int buildStopBit(Enum_StopBit stopbit, struct termios* term);
static Enum_StopBit getStopBit(struct termios* term);

static int buildParity(Enum_ParityBit parity, struct termios* term);
static Enum_ParityBit getParity(struct termios* term);

static int buildFlowControl(Enum_FlowCtrl fc, struct termios* term);
static Enum_FlowCtrl getFlowControl(struct termios* term);


/*-----------------------------------------------------------------------------------------------*/
/**
      Open uart device api
       @brief
         @param[in] port     uart devices path
         @param[in] flags    property setting
       @return
         -fd   file descriptor
         -QL_ERR_TARGET_NOT_EXIST   devices does not exist
      */
/*-----------------------------------------------------------------------------------------------*/
int Ql_UART_Open(const char* port, Enum_BaudRate baudrate, Enum_FlowCtrl flowctrl)
{
    int fd;
    int iRet;

    printf("Device port=%s \n", port);
    
    //check the value range of arguments
    if (NULL == port)
    {        
        printf("Device does not exist, open fail \n");
        return  QL_ERR_TARGET_NOT_EXIST;
    }
    
    fd = open(port, O_RDWR | O_NOCTTY | O_NONBLOCK);
    if(fd < 0)
    {
        printf("uart_open open  fail ,rc=%d  \n", fd);
        return  QL_ERR_FAILED;
    }

    /* Start: If need, to modify uart dcb config */
    ST_UARTDCB dcb = {
        .flowctrl = flowctrl,    //none flow control
        .databit = DB_CS8,  //databit: 8
        .stopbit = SB_1,    //stopbit: 1
        .parity = PB_NONE,  //parity check: none
        .baudrate = baudrate    //baudrate: 115200
    };

    if (  QL_ERR_OK != Ql_UART_SetDCB(fd, &dcb) ) {
        printf("Ql_UART_SetDCB open  fail , fd=%d  \n", fd);
        close(fd);
        return  QL_ERR_FAILED;
    }
    iRet = Ql_UART_GetDCB(fd, &dcb);
    printf("GET DCB ret: %d: baudrate: %d, flowctrl: %d, databit: %d, stopbit: %d, paritybit: %d\n",
                iRet, dcb.baudrate, dcb.flowctrl, dcb.databit, dcb.stopbit, dcb.parity);

    return fd;
}

int Ql_UART_SetDCB(int fd, ST_UARTDCB *dcb)
{
    struct termios term;

    //check the value range of arguments
    if ((fd < 0) || (NULL == dcb))
    {
        return  QL_ERR_INVALID_ARG;
    }
    
    bzero(&term, sizeof(term));
    cfmakeraw(&term);
    term.c_cflag |= CREAD;
    tcgetattr(fd, &term);
    
    buildBaudrate(dcb->baudrate, &term);
    buildDataBit(dcb->databit, &term);
    buildStopBit(dcb->stopbit, &term);
    buildParity(dcb->parity, &term);
    buildFlowControl(dcb->flowctrl, &term);

    term.c_iflag &= ~ICRNL;
    term.c_iflag &= ~INLCR;
    term.c_iflag |= IGNBRK;

    term.c_oflag &= ~OCRNL;
    term.c_oflag &= ~ONLCR;
    term.c_oflag &= ~OPOST;

    term.c_lflag &= ~ICANON;
    term.c_lflag &= ~ISIG;
    term.c_lflag &= ~IEXTEN;
    term.c_lflag &= ~(ECHO|ECHOE|ECHOK|ECHONL|ECHOCTL|ECHOPRT|ECHOKE);

    tcsetattr(fd, TCSANOW, &term);
    tcflush(fd, TCIOFLUSH);

    return QL_ERR_OK;
}

int Ql_UART_GetDCB(int fd, ST_UARTDCB *dcb)
{
    struct termios term;

    //check the value range of arguments
    if ((fd < 0) || (NULL == dcb))
    {
        return QL_ERR_INVALID_ARG;
    }

    bzero(&term, sizeof(term));
    cfmakeraw(&term);
    term.c_cflag |= CREAD;
    tcgetattr(fd, &term);
    dcb->baudrate = getBaudrate(&term);
    dcb->flowctrl = getFlowControl(&term);
    dcb->databit = getDataBit(&term);
    dcb->stopbit = getStopBit(&term);
    dcb->parity = getParity(&term);
    return QL_ERR_OK;
}

int Ql_UART_Write(int fd, const char* buf, unsigned int buf_len)
{
    size_t size;
    size_t size_to_wr;
    ssize_t size_written;

    //check the value range of arguments
    if ((fd < 0) || (NULL == buf))
    {
        return QL_ERR_INVALID_ARG;
    }

    for(size = 0; size < buf_len;)
    {
        size_to_wr = buf_len - size;
        if( size_to_wr > UART_WRITE_MAX_SIZE)
            size_to_wr = UART_WRITE_MAX_SIZE;

        size_written = write(fd, &buf[size], size_to_wr);

        if (size_written==-1) {
            printf("Cannot write on uart: %s",strerror(errno));
            return QL_ERR_FAILED;
        }

        //printf("Uart Write: %d\n", size_written);
        size += size_written;

        if(size_written != size_to_wr)
            break;
    }

    return size;
}

int Ql_UART_Read(int fd, char* buf, unsigned int buf_len)
{
    //check the value range of arguments
    if ((fd < 0) || (NULL == buf))
    {
        return QL_ERR_INVALID_ARG;
    }
    return read(fd, buf, buf_len);
}

int Ql_UART_IoCtl(int fd, unsigned int cmd, void* pValue)
{
    //check the value range of arguments
    if ((fd < 0) || (NULL == pValue))
    {
        return QL_ERR_INVALID_ARG;
    }
    return ioctl(fd, cmd, pValue);
}

int Ql_UART_Close(int fd)
{
    return close(fd);
}

/*-----------------------------------------------------------------------------------------------*/
/**
        Setting  uart  odd  or even  parity 
       @brief
         @param[in] parity     odd  or even  parity parameter
         @param[in] term       struct termios
       @return
         -QL_ERR_OK       if it ok
         -QL_ERR_FAILED   if it write failed
      */
/*-----------------------------------------------------------------------------------------------*/
static int buildParity(Enum_ParityBit parity, struct termios* term)
{
    if (PB_NONE == parity)
    {
        term->c_cflag &= ~PARENB;
    }
    else if (PB_ODD == parity)
    {
        term->c_cflag |= (PARENB | PARODD);
    }
    else if (PB_EVEN == parity)
    {
        term->c_cflag &= ~PARODD;
        term->c_cflag |= PARENB;
    }
    else {
        return QL_ERR_FAILED;
    }
    
    return QL_ERR_OK;
}

/*-----------------------------------------------------------------------------------------------*/
/**
        Get  uart  odd  or even  parity 
       @brief
         @param[in] term       struct termios
       @return
         -0  if it none parity 
         -1  if it odd parity
         -2  if it even parity
      */
/*-----------------------------------------------------------------------------------------------*/
static Enum_ParityBit getParity(struct termios* term)
{
    Enum_ParityBit parity = 0;

    switch(term->c_cflag & (PARENB | PARODD))
    {
    case (PARODD | PARENB):
        parity = PB_ODD;
        break;
    case ((~PARODD) & PARENB):
        parity = PB_EVEN;
        break;
    default:
        parity = PB_NONE;
        break;
    }
    
    return parity;
}

/*-----------------------------------------------------------------------------------------------*/
/**
        Setting  uart  flow control
       @brief
           @param[in] fc     Hardware Flow Control (rtscts) /Software Flow Control (xon/xoff)
           @param[in] term       struct termios
       @return
          -QL_ERR_OK  if it ok
          -QL_ERR_FAILED   if it write failed
      */
/*-----------------------------------------------------------------------------------------------*/
static int buildFlowControl(Enum_FlowCtrl fc, struct termios* term)
{
    if (FC_NONE == fc)
    {
        term->c_cflag &= ~CRTSCTS;
        term->c_iflag &= ~(IXON | IXOFF);
    }
    else if (FC_RTSCTS == fc)
    {
        term->c_cflag |= CRTSCTS;
        term->c_iflag &= ~(IXON | IXOFF);
    }
    else if (FC_XONXOFF == fc)
    {
        term->c_cflag &= ~CRTSCTS;
        term->c_iflag |= (IXON | IXOFF);
    }else{
        return QL_ERR_FAILED;
    }
    
    return QL_ERR_OK;
}

/*-----------------------------------------------------------------------------------------------*/
/**
        Get  uart  flow control
       @brief
           @param[in] term       struct termios
       @return
          -0   None Flow Control
          -1   Hardware Flow Control (rtscts)
          -2   Software Flow Control (xon/xoff)
      */
/*-----------------------------------------------------------------------------------------------*/
static Enum_FlowCtrl getFlowControl(struct termios* term)
{
    Enum_ParityBit fc = 0;

    if(((term->c_cflag & CRTSCTS) == 0) && ((term->c_iflag & (IXON | IXOFF)) == 0))
        fc = FC_NONE;
    else if(((term->c_cflag & CRTSCTS) == CRTSCTS) && ((term->c_iflag & (IXON | IXOFF)) == 0))
        fc = FC_RTSCTS;
    else if(((term->c_cflag & CRTSCTS) == 0) && ((term->c_iflag & (IXON | IXOFF)) == (IXON | IXOFF)))
        fc = FC_XONXOFF;
    
    return fc;
}

/*-----------------------------------------------------------------------------------------------*/
/**
        Setting  uart date bit
       @brief           
           @param[in] databit     set parameter
           @param[in] term       struct termios
       @return
          -QL_ERR_OK  if it ok
          -QL_ERR_FAILED   if it write failed
      */
/*-----------------------------------------------------------------------------------------------*/
static int buildDataBit(Enum_DataBit databit, struct termios* term)
{
    term->c_cflag &= ~CSIZE;
    
    switch (databit)
    {
    case 5:
        term->c_cflag |= CS5;
        break;
    case 6:
        term->c_cflag |= CS6;
        break;
    case 7:
        term->c_cflag |= CS7;
        break;
    case 8:
        term->c_cflag |= CS8;
        break;
    default:
        return QL_ERR_FAILED;
    }
    return QL_ERR_OK;
}

/*-----------------------------------------------------------------------------------------------*/
/**
        Get   data bit  for uart
       @brief
           @param[in] term       struct termios
       @return
          -5   5bit data
          -6   6bit data
          -7   7bit data
          -8   8bit data
      */
/*-----------------------------------------------------------------------------------------------*/
static Enum_DataBit getDataBit(struct termios* term)
{
    Enum_DataBit databit = 0;

    switch(term->c_cflag & CSIZE)
    {
    case CS5:
        databit = DB_CS5;
        break;
    case CS6:
        databit = DB_CS6;
        break;
    case CS7:
        databit = DB_CS7;
        break;
    case CS8:
        databit = DB_CS8;
        break;
    default:
        break;
    }
    return databit;
}

/*-----------------------------------------------------------------------------------------------*/
/**
        Setting   stop bit  for uart
       @brief          
           @param[in] stopbit     set parameter
           @param[in] term       struct termios
       @return
          -QL_ERR_OK  if it ok
          -QL_ERR_FAILED   if it write failed
      */
/*-----------------------------------------------------------------------------------------------*/
static int buildStopBit(Enum_StopBit stopbit, struct termios* term)
{
    if (stopbit == 1)
    {
        term->c_cflag &= ~CSTOPB;
    }
    else if (stopbit == 2)
    {
        term->c_cflag |= CSTOPB;
    } else {
        return QL_ERR_FAILED;
    }
    
    return QL_ERR_OK;
}

/*-----------------------------------------------------------------------------------------------*/
/**
        Get   stop bit  for uart
       @brief          
           @param[in] term       struct termios
       @return
          -1    1bit stop bit
          -2    2bit stop bit
      */
/*-----------------------------------------------------------------------------------------------*/
static Enum_StopBit getStopBit(struct termios* term)
{
    Enum_StopBit stopbit = 0;

    switch(term->c_cflag & CSTOPB)
    {
    case (00000):
        stopbit = SB_1;
        break;
    case (00100):
        stopbit = SB_2;
        break;
    default:
        break;
    }
    
    return stopbit;
}

/*-----------------------------------------------------------------------------------------------*/
/**
        Setting   baud rate  for uart
       @brief          
           @param[in] baudrate     set parameter
           @param[in] term       struct termios
       @return
          -QL_ERR_OK  if it ok
          -QL_ERR_FAILED   if it write failed
      */
/*-----------------------------------------------------------------------------------------------*/
static int buildBaudrate(Enum_BaudRate baudrate, struct termios* term)
{
  switch (baudrate)
  {
    case B_600:
    {
        cfsetspeed(term, B600);
        break;
    }
    case B_1200:
    {
        cfsetspeed(term, B1200);
        break;
    }
    case B_2400:
    {
        cfsetspeed(term, B2400);
        break;
    }
    case B_4800:
    {
        cfsetspeed(term, B4800);
        break;
    }
    case B_9600:
    {
        cfsetspeed(term, B9600);
        break;
    }
    case B_19200:
    {
        cfsetspeed(term, B19200);
        break;
    }
    case B_38400:
    {
        cfsetspeed(term, B38400);
        break;
    }
    case B_57600:
    {
        cfsetspeed(term, B57600);
        break;
    }
    case B_115200:
    {
        cfsetspeed(term, B115200);
        break;
    }
    case B_230400:
    {
        cfsetspeed(term, B230400);
        break;
    }
    case B_460800:
    {
        cfsetspeed(term, B460800);
        break;
    }
    case B_921600:
    {
        cfsetspeed(term, B921600);
        break;
    }
    default:
        printf("SERIAL: unsupported baudrate: %d\n", baudrate);
        return QL_ERR_FAILED;
  }
  return QL_ERR_OK;
}

/*-----------------------------------------------------------------------------------------------*/
/**
        Get  baud rate  for uart
       @brief          
           @param[in] term       struct termios
       @return
          -S   baud rate
      */
/*-----------------------------------------------------------------------------------------------*/
static Enum_BaudRate getBaudrate(struct termios* term)
{
    Enum_BaudRate s = B_115200;
    if (cfgetispeed(term) != cfgetospeed(term))
    printf("SERIAL: Input and Output speed are different. Check the serial configuration !\n");
    
    switch (cfgetispeed(term))
    {
    case B600:
        s = B_600;
        break;
    case B1200:
        s = B_1200;
        break;
    case B2400:
        s = B_2400;
        break;
    case B4800:
        s = B_4800;
        break;
    case B9600:
        s = B_9600;
        break;
    case B19200:
        s = B_19200;
        break;
    case B38400:
        s = B_38400;
        break;
    case B57600:
        s = B_57600;
        break;
    case B115200:
        s = B_115200;
        break;
    case B230400:
        s = B_230400;
        break;
    case B460800:
        s = B_460800;
        break;
    case B921600:
        s = B_921600;
        break;
    default:
        printf("SERIAL: Unknown speed setting, using default: %d!\n", s);
        break;
    }
    return s;
}
